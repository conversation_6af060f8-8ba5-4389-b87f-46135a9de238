# -*- coding: utf-8 -*-
{
    "name": "Stairmaster - Sale",
    "version": "********.0",
    "category": "Sale",
    "description": """
    """,
    "author": "ITMS Group",
    "website": "",
    "depends": [
        "base",
        "mail",
        "base_automation",
        "sale_management",
        "project",
        "sale_project",
        "purchase_stock",
        "sale_crm",
        "sales_team",
        "groq_document_processor",
        "document_classification_groq",
        "stairmaster_install_address",
        "stairmaster_contact_extened",
    ],
    "sequence": 0,
    "data": [
        # 'security/',
        "security/ir.model.access.csv",
        "security/res_groups.xml",
        # 'data/',
        "data/ir_config_parameter_data.xml",
        "data/email_incoming_data.xml",
        "data/groq_document_processor_coral_homes.xml",
        "data/groq_document_processor_plantation_homes.xml",
        "data/groq_document_processsor_clarendon.xml",
        "data/room_room_data.xml",
        "data/product_data.xml",
        "data/quote_request_data.xml",
        "data/sale_order_stage_data.xml",
        "data/base_automation.xml",
        # 'views/',
        # "views/crm_lead_views.xml",
        "views/sales_team_view.xml",
        "views/sale_order_view.xml",
        "views/sale_order_quotes_view.xml",
        "views/sale_order_orders_view.xml",
        "views/sale_order_callups_view.xml",
        "views/sale_order_warranty_view.xml",
        "views/sale_order_stairbiz_view.xml",
        "views/project_project_view.xml",
        "views/product_template_view.xml",
        "views/product_attribute_views.xml",
        "views/sale_order_stage_view.xml",
        "views/quote_request_type_views.xml"
        # 'menu/',
    ],
    "assets": {
        "web.assets_backend": [
            #"stairmaster_sale/static/src/scss/*.scss",
            "stairmaster_sale/static/src/views/form/*",
        ],
    },
    "test": [],
    "demo": [],
    "installable": True,
    "application": True,
    "license": "LGPL-3",
}
