from odoo import models, fields, api
import logging
import subprocess

_logger = logging.getLogger(__name__)

class StairbizIntegrationConfig(models.Model):
    _name = "stairbiz.integration.config"
    _description = "Stairbiz Integration Configuration"

    name = fields.Char(required=True)
    connection_string = fields.Char("ODBC Connection String",
                                  help="Example: DRIVER={FreeTDS};SERVER=myserver;DATABASE=mydatabase;UID=myusername;PWD=mypassword")
    active = fields.Boolean(default=True)
    
    # Additional configuration fields
    driver = fields.Selection([
        ('FreeTDS', 'FreeTDS'),
        ('ODBC Driver 18 for SQL Server', 'ODBC Driver 18 for SQL Server'),
        ('ODBC Driver 17 for SQL Server', 'ODBC Driver 17 for SQL Server'),
        ('SQL Server', 'SQL Server'),
        ('MySQL', 'MySQL')
    ], string="ODBC Driver", default='FreeTDS')
    
    server = fields.Char("Server", help="Server address or IP (e.g. localhost, ***********, *************)")
    port = fields.Char("Port", help="Database server port (e.g. 1433 for SQL Server, 3306 for MySQL)")
    database = fields.Char("Database")
    username = fields.Char("Username")
    password = fields.Char("Password", invisible_password=True)

    active = fields.Boolean(string="Active", default=True)
    
    @api.model
    def get_next_reference(self, batch_type):
        """Get the next reference number for the specified batch type"""
        config = self.search([('active', '=', True)], limit=1)
        if not config:
            return False
            
        if batch_type == 'quote':
            prefix = config.quote_prefix
            config.current_quote_number += 1
            return f"{prefix}{config.current_quote_number - 1}"
        elif batch_type == 'job':
            prefix = config.job_prefix
            config.current_job_number += 1
            return f"{prefix}{config.current_job_number - 1}"
        elif batch_type == 'design':
            prefix = config.design_prefix
            config.current_design_number += 1
            return f"{prefix}{config.current_design_number - 1}"
        
        return False

    def action_test_connection(self):
        """Test the connection to Stairbiz"""
        self.ensure_one()
        try:
            import pyodbc
            connection = pyodbc.connect(self.connection_string)
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            connection.close()
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Connection Test',
                    'message': 'Connection to Stairbiz successful!',
                    'sticky': False,
                    'type': 'success',
                }
            }
        except ImportError:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Connection Test',
                    'message': 'PyODBC module is not installed. Please install it with: pip install pyodbc',
                    'sticky': False,
                    'type': 'warning',
                }
            }
        except pyodbc.Error as e:
            error_msg = str(e)
            if "Can't open lib" in error_msg and "file not found" in error_msg:
                driver_name = self.driver
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Connection Test',
                        'message': f'ODBC Driver "{driver_name}" not found. Please install the appropriate driver for your system.',
                        'sticky': False,
                        'type': 'warning',
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Connection Test',
                        'message': f'Connection to Stairbiz failed: {error_msg}',
                        'sticky': False,
                        'type': 'warning',
                    }
                }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Connection Test',
                    'message': f'Connection to Stairbiz failed: {str(e)}',
                    'sticky': False,
                    'type': 'warning',
                }
            }
    
    def action_generate_connection_string(self):
        """Generate the connection string from individual fields"""
        self.ensure_one()
        
        if not self.server or not self.database:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Error',
                    'message': 'Server and Database are required',
                    'sticky': False,
                    'type': 'warning',
                }
            }
        
        connection_string = f"DRIVER={{{self.driver}}};SERVER={self.server};"
        
        if self.port:
            connection_string += f"PORT={self.port};"
        
        connection_string += f"DATABASE={self.database};"
        
        if self.username:
            connection_string += f"UID={self.username};"
        
        if self.password:
            connection_string += f"PWD={self.password};"
        
        self.connection_string = connection_string
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Success',
                'message': 'Connection string generated',
                'sticky': False,
                'type': 'success',
            }
        }
    
    def action_check_available_drivers(self):
        """Check which ODBC drivers are available on the system"""
        self.ensure_one()
        
        try:
            import pyodbc
            drivers = pyodbc.drivers()
            
            if not drivers:
                # Try to get drivers using odbcinst if pyodbc.drivers() returns empty
                try:
                    result = subprocess.run(['odbcinst', '-q', '-d'], capture_output=True, text=True)
                    if result.returncode == 0:
                        # Parse the output which has format [Driver1]\n[Driver2]\n...
                        drivers = [line.strip('[]') for line in result.stdout.split('\n') if line.startswith('[') and line.endswith(']')]
                except (subprocess.SubprocessError, FileNotFoundError):
                    _logger.warning("Could not run odbcinst command")
            
            if drivers:
                driver_list = "\n- " + "\n- ".join(drivers)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Available ODBC Drivers',
                        'message': f'The following ODBC drivers are available on the system:{driver_list}',
                        'sticky': True,
                        'type': 'success',
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': 'Available ODBC Drivers',
                        'message': 'No ODBC drivers were found on the system. Please install appropriate drivers.',
                        'sticky': True,
                        'type': 'warning',
                    }
                }
        except ImportError:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Error',
                    'message': 'PyODBC module is not installed. Please install it with: pip install pyodbc',
                    'sticky': False,
                    'type': 'warning',
                }
            }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Error',
                    'message': f'Failed to check available drivers: {str(e)}',
                    'sticky': False,
                    'type': 'warning',
                }
            }