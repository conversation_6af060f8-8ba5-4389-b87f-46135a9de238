import json
import logging
import base64
import os
import traceback
from datetime import datetime, timedelta

import requests
from odoo import _, api, fields, models
from odoo.osv import expression
from odoo.tools import html2plaintext

_logger = logging.getLogger(__name__)

# Constants for Google APIs
GOOGLE_MAP_PLACES = "https://maps.googleapis.com/maps/api/place"


class StairBizQuote(models.Model):
    _name = "stairbiz.stairbiz"
    _description = "StairBiz Quote"
    _inherit = ["mail.thread", "mail.activity.mixin"]

    name = fields.Char(string="Quote Number", tracking=True)
    order_id = fields.Many2one(
        "sale.order",
        string="Order",
    )
    stairbiz_server_type = fields.Selection(
        [
            ("quote", "Quote"),
            ("design", "Design"),
            ("standards", "Standards"),
            ("dev/test", "Dev/Test"),
            ("archive1", "Archive1"),
            ("archive2", "Archive2"),
            ("etc", "etc.."),
        ],
        string="StairBiz Server Type",
        default="quote",
    )

    # StairBiz Job Generation fields
    stairbiz_job_generated = fields.Boolean(
        string="StairBiz Job Generated", default=False
    )
    stairbiz_job_filename = fields.Char(string="StairBiz Job Filename", readonly=True)

    # ITMS Template Design
    stairbiz_template = fields.Selection(
        [
            ("residential", "Residential Stairs"),
            ("commercial", "Commercial Project"),
            ("renovation", "Renovation"),
            ("custom", "Custom Design"),
            ("spiral", "Spiral Staircase"),
        ],
        string="Job Template",
        tracking=True,
    )
    stairbiz_status = fields.Selection(
        [
            ("quote", "Quote"),
            ("design", "Design"),
            ("production", "Production"),
            ("installation", "Installation"),
            ("completed", "Completed"),
        ],
        string="StairBiz Status",
        default="quote",
        tracking=True,
    )

    # ITMS Status
    odoo_stairbiz_status = fields.Selection(
        [
            ("available", "Available"),
            ("allocated", "Allocated"),
        ],
        string="Odoo StairBiz Status",
        default="available",
        tracking=True,
    )
    allocation_date = fields.Datetime(string="Allocation Date")
    stairbiz_sync_date = fields.Datetime(string="StairBiz Sync Date")

    # Project fields
    project_folder_id = fields.Many2one(
        "stairbiz_quotes.jobfolder", string="Project Folder"
    )
    project_name = fields.Char(string="Project Name")

    # Job fields
    created_by = fields.Char(string="Created By")
    job_note1 = fields.Char(string="Job Note 1")
    job_note2 = fields.Text(string="Job Note 2")
    job_name = fields.Char(string="Job Name")
    job_number = fields.Char(string="Job Number")
    job_status = fields.Char(string="Job Status")
    job_date = fields.Date(string="Job Date")
    client_name = fields.Char(string="Client Name")
    client_email = fields.Char(string="Client Email")
    client_note = fields.Text(string="Client Note")
    client_mobile = fields.Char(string="Client Mobile")
    company_number = fields.Char(string="Company Number")
    contact_name = fields.Char(string="Contact Name")
    critical_note = fields.Text(string="Critical Note")
    cust_id = fields.Char(string="Customer ID")
    def_contact = fields.Boolean(string="Default Contact")
    discount = fields.Float(string="Discount")

    # Flag fields
    flag_color = fields.Selection(
        [
            ("red", "Red"),
            ("orange", "Orange"),
            ("yellow", "Yellow"),
            ("green", "Green"),
            ("blue", "Blue"),
            ("purple", "Purple"),
        ],
        string="Flag Color",
    )
    flag_date = fields.Date(string="Flag Date")
    flag_notes = fields.Text(string="Flag Notes")
    flag_user = fields.Char(string="Flag User")
    job_color = fields.Selection(
        [
            ("red", "Red"),
            ("orange", "Orange"),
            ("yellow", "Yellow"),
            ("green", "Green"),
            ("blue", "Blue"),
            ("purple", "Purple"),
        ],
        string="Job Color",
    )
    purchase_order = fields.Char(string="Purchase Order", size=20)

    # StairBiz Quote Information
    quote_number = fields.Char(string="Quote Number", size=20, tracking=True)
    quote_date = fields.Date(string="Quote Date", tracking=True)
    quote_price = fields.Float(string="Quote Price", tracking=True)
    quote_representative = fields.Many2one(
        "res.users", string="Quote Representative", tracking=True
    )
    sales_person = fields.Char(string="Sales Person")
    scenario_name = fields.Char(string="Scenario Name", size=30)

    # StairBiz Technical Specifications
    dispatch_method = fields.Selection(
        [
            ("install", "Install"),
            ("supply", "Supply"),
        ],
        string="Dispatch Method",
    )
    quoted_from_plan = fields.Boolean(string="Quoted from Plan")

    # Basic measurements
    total_rise = fields.Float(string="Total Rise")
    total_rise_uom = fields.Many2one(
        "uom.uom",
        string="Total Rise UOM",
    )

    number_of_risers = fields.Integer(string="No. of Risers")
    number_of_risers_uom = fields.Many2one(
        "uom.uom",
        string="No. of Risers UOM",
    )

    risers_thickness = fields.Float(string="Risers Thickness")
    risers_thickness_uom = fields.Many2one(
        "uom.uom",
        string="Risers Thickness UOM",
    )

    # Components
    treads = fields.Float(string="Treads")
    treads_uom = fields.Many2one(
        "uom.uom",
        string="Treads UOM",
    )
    treads_grade = fields.Many2one(
        "product.product",
        string="Treads Grade",
        domain="[('categ_id.name', '=', 'Treads')]",
    )

    treads_nosing = fields.Char(string="Treads Nosing")
    treads_nosing_uom = fields.Many2one(
        "uom.uom",
        string="Treads Nosing UOM",
    )
    treads_nosing_grade = fields.Many2one(
        "product.product",
        string="Treads Nosing Grade",
        domain="[('categ_id.name', '=', 'Treads Nosing')]",
    )

    base_treads = fields.Char(string="Base Treads")
    base_treads_uom = fields.Many2one(
        "uom.uom",
        string="Base Treads UOM",
    )
    base_treads_grade = fields.Many2one(
        "product.product",
        string="Base Treads Grade",
        domain="[('categ_id.name', '=', 'Base Treads')]",
    )

    open_side_strings = fields.Float(string="Open Side Strings")
    open_side_strings_uom = fields.Many2one(
        "uom.uom",
        string="Open Side Strings UOM",
    )
    open_side_strings_type = fields.Char(string="Open Side Strings Type")
    open_side_strings_grade = fields.Many2one(
        "product.product",
        string="Open Side Strings Grade",
        domain="[('categ_id.name', '=', 'Open Side Strings')]",
    )

    wall_strings = fields.Float(string="Wall Strings")
    wall_strings_uom = fields.Many2one(
        "uom.uom",
        string="Wall Strings UOM",
    )
    wall_strings_type = fields.Char(string="Wall Strings Type")
    wall_strings_grade = fields.Many2one(
        "product.product",
        string="Wall Strings Grade",
        domain="[('categ_id.name', '=', 'Wall Strings')]",
    )

    skirting = fields.Char(string="Skirting to Landings")
    skirting_uom = fields.Many2one(
        "uom.uom",
        string="Skirting UOM",
    )
    skirting_grade = fields.Many2one(
        "product.product",
        string="Skirting Grade",
        domain="[('categ_id.name', '=', 'Skirting')]",
    )

    skirting_landings = fields.Char(string="Skirting to Landings")

    landing_winders = fields.Char(string="Landing Winder")
    landing_winders_uom = fields.Many2one(
        "uom.uom",
        string="Landing Winder UOM",
    )
    landing_winders_grade = fields.Many2one(
        "product.product",
        string="Landing Winder Grade",
        domain="[('categ_id.name', '=', 'Landing Winder')]",
    )

    newel_style_timber = fields.Char(string="Newel Style/Timber")
    newel_style_timber_uom = fields.Many2one(
        "uom.uom",
        string="Newel Style/Timber UOM",
    )
    newel_style_timber_grade = fields.Many2one(
        "product.product",
        string="Newel Style/Timber Grade",
        domain="[('categ_id.name', '=', 'Newel Style/Timber')]",
    )

    newel_top = fields.Char(string="Newel Top")
    newel_top_uom = fields.Many2one(
        "uom.uom",
        string="Newel Top UOM",
    )
    newel_top_grade = fields.Many2one(
        "product.product",
        string="Newel Top Grade",
        domain="[('categ_id.name', '=', 'Newel Top')]",
    )

    baluster_style = fields.Char(string="Baluster Style")
    baluster_style_uom = fields.Many2one(
        "uom.uom",
        string="Baluster Style UOM",
    )
    baluster_style_grade = fields.Many2one(
        "product.product",
        string="Baluster Style Grade",
        domain="[('categ_id.name', '=', 'Baluster Style')]",
    )

    handrail_style = fields.Char(string="Handrail Style")
    handrail_style_uom = fields.Many2one(
        "uom.uom",
        string="Handrail Style UOM",
    )
    handrail_style_grade = fields.Many2one(
        "product.product",
        string="Handrail Style Grade",
        domain="[('categ_id.name', '=', 'Handrail Style')]",
    )

    raking_brail_style = fields.Char(string="Raking B'rail Style")
    raking_brail_style_uom = fields.Many2one(
        "uom.uom",
        string="Raking B'rail Style UOM",
    )
    raking_brail_style_grade = fields.Many2one(
        "product.product",
        string="Raking B'rail Style Grade",
        domain="[('categ_id.name', '=', 'Raking B'rail Style')]",
    )

    void_trim = fields.Char(string="Void Trim")
    void_trim_uom = fields.Many2one(
        "uom.uom",
        string="Void Trim UOM",
    )
    void_trim_grade = fields.Many2one(
        "product.product",
        string="Void Trim Grade",
        domain="[('categ_id.name', '=', 'Void Trim')]",
    )

    level_brail_style = fields.Char(string="Level B'rail Style")
    level_brail_style_uom = fields.Many2one(
        "uom.uom",
        string="Level B'rail Style UOM",
    )
    level_brail_style_grade = fields.Many2one(
        "product.product",
        string="Level B'rail Style Grade",
        domain="[('categ_id.name', '=', 'Level B'rail Style')]",
    )

    wall_rail_style = fields.Char(string="Wall Rail Style")
    wall_rail_style_uom = fields.Many2one(
        "uom.uom",
        string="Wall Rail Style UOM",
    )
    wall_rail_style_grade = fields.Many2one(
        "product.product",
        string="Wall Rail Style Grade",
        domain="[('categ_id.name', '=', 'Wall Rail Style')]",
    )

    wall_rail_bracket = fields.Char(string="Wall Rail Bracket")
    wall_rail_bracket_style = fields.Char(string="Wall Rail Bracket Style")
    wall_rail_bracket_style_uom = fields.Many2one(
        "uom.uom",
        string="Wall Rail Bracket Style UOM",
    )
    wall_rail_bracket_style_grade = fields.Many2one(
        "product.product",
        string="Wall Rail Bracket Style Grade",
        domain="[('categ_id.name', '=', 'Wall Rail Bracket Style')]",
    )

    # Client fields
    is_owner = fields.Boolean(string="Is Owner")
    client_phone = fields.Char(string="Client Phone", size=21)
    client_fax = fields.Char(string="Client Fax", size=21)
    mobile = fields.Char(string="Mobile", size=21)
    referred_by = fields.Char(string="Referred By", size=20)
    salutation = fields.Char(string="Salutation", size=30)
    sched_colors = fields.Char(string="Sched Colors", size=3)
    show_critical = fields.Boolean(string="Show Critical Note")
    client_state_id = fields.Many2one(
        "res.country.state",
        string="State",
        ondelete="restrict",
        default=lambda self: self.env["res.country.state"].search(
            [("name", "=", "Queensland")], limit=1
        ),
    )
    client_state = fields.Char(related="client_state_id.name")
    client_status = fields.Selection(
        [("temporary", "Temporary"), ("permanent", "Permanent")], string="Client Status"
    )
    client_street = fields.Char(string="Street", size=30)
    client_city = fields.Char(string="City", size=20)
    client_suburb = fields.Char(string="Suburb", size=25)
    client_tag = fields.Char(string="Tag", size=8)
    client_zip = fields.Char(string="Zip Code", change_default=True)

    # Contact fields
    contact_name = fields.Char(string="Contact Name", size=30)
    contact_note = fields.Text(string="Contact Note")
    contact_def_contact = fields.Boolean(string="Default Contact")
    contact_email = fields.Char(string="Email", size=40)
    contact_fax = fields.Char(string="Fax", size=21)
    contact_mobile = fields.Char(string="Mobile", size=21)
    contact_phone = fields.Char(string="Phone", size=21)
    contact_role = fields.Char(string="Role", size=30)
    contact_salutation = fields.Char(string="Salutation", size=30)
    contact_status = fields.Selection(
        [("temporary", "Temporary"), ("permanent", "Permanent")],
        string="Contact Status",
    )
    contact_tag = fields.Char(string="Tag", size=8)
    contact_user_field = fields.Char(string="User Field", size=30)

    # Site fields
    site_address_id = fields.Many2one(
        "res.partner",
        string="Site Address",
        domain="[('type_contact', '=', 'site_addresses')]",
    )
    site_city = fields.Char(string="City", size=20)
    cross_street = fields.Char(string="Cross Street", size=25)
    is_new_home = fields.Boolean(string="Is New Home")
    map_ref = fields.Char(string="Map Ref", size=8)
    measure_date = fields.Datetime(string="Measure Date")
    site_mobile = fields.Char(string="Mobile", size=21)
    site_phone = fields.Char(string="Phone", size=21)
    site_contact = fields.Many2one(
        "res.partner",
        string="Site Contact",
        domain="[('type_contact', '=', 'site_addresses')]",
    )
    site_note1 = fields.Text(string="Site Note 1")
    site_note2 = fields.Text(string="Site Note 2")
    site_state = fields.Char(string="State", size=10)
    site_street = fields.Char(string="Street", size=35)
    site_suburb = fields.Char(string="Suburb", size=20)
    site_zip = fields.Char(string="Zip", size=8)
    site_country = fields.Char(string="Country", size=20)

    # Technical fields
    brick_lower = fields.Boolean(string="Brick Lower")
    brick_upper = fields.Boolean(string="Brick Upper")
    briefing = fields.Boolean(string="Briefing")
    cnc_done = fields.Boolean(string="CNC Done")
    details_note = fields.Text(string="Details Note")
    dispose_date = fields.Date(string="Dispose Date")
    dispose_mode = fields.Selection(
        [("pickup", "Pick Up"), ("deliver", "Deliver"), ("install", "Install")],
        string="Dispose Mode",
    )
    from_plan = fields.Boolean(string="From Plan")
    lower_floor = fields.Selection(
        [
            ("tile", "Tile"),
            ("t&g", "T&G"),
            ("sheet", "Sheet"),
            ("concrete", "Concrete"),
        ],
        string="Lower Floor",
    )
    power_on = fields.Boolean(string="Power On")
    re_measure = fields.Boolean(string="Re-Measure")
    requires_cnc = fields.Boolean(string="Requires CNC")
    schedule_cnc = fields.Date(string="Schedule CNC")
    schedule_production = fields.Date(string="Schedule Production")
    schedule_install = fields.Date(string="Schedule Install")
    show_critical_note = fields.Boolean(string="Show Critical Note")
    stain = fields.Boolean(string="Stain")
    travel_dollars = fields.Float(string="Travel Dollars")
    travel_dollars_b = fields.Float(string="Travel Dollars B")
    travel_minutes = fields.Integer(string="Travel Minutes")
    travel_minutes_b = fields.Integer(string="Travel Minutes B")
    tread_protect = fields.Boolean(string="Tread Protect")
    upper_floor = fields.Selection(
        [
            ("tile", "Tile"),
            ("t&g", "T&G"),
            ("sheet", "Sheet"),
            ("concrete", "Concrete"),
        ],
        string="Upper Floor",
    )
    we_cut_floor = fields.Boolean(string="We Cut Floor")
    we_cut_wall = fields.Boolean(string="We Cut Wall")
    we_paint = fields.Boolean(string="We Paint")

    # Quote fields
    quote_discount = fields.Float(string="Discount")
    profit_percent = fields.Float(string="Profit Percent")
    tax_percent1 = fields.Float(string="Tax Percent 1")
    tax_percent2 = fields.Float(string="Tax Percent 2")

    # Payment fields
    pay1_percent = fields.Integer(string="Pay 1 Percent")
    pay2_percent = fields.Integer(string="Pay 2 Percent")
    pay2_days = fields.Integer(string="Pay 2 Days")
    pay3_days = fields.Integer(string="Pay 3 Days")
    terms2 = fields.Selection(
        [
            ("before_start", "Before Start"),
            ("after_start", "After Start"),
            ("before_end", "Before End"),
            ("after_end", "After End"),
        ],
        string="Terms 2",
    )
    terms3 = fields.Selection(
        [
            ("before_start", "Before Start"),
            ("after_start", "After Start"),
            ("before_end", "Before End"),
            ("after_end", "After End"),
        ],
        string="Terms 3",
    )
    paid_amt1 = fields.Float(string="Paid Amount 1")
    paid_amt2 = fields.Float(string="Paid Amount 2")
    paid_amt3 = fields.Float(string="Paid Amount 3")
    paid_date1 = fields.Date(string="Paid Date 1")
    paid_date2 = fields.Date(string="Paid Date 2")
    paid_date3 = fields.Date(string="Paid Date 3")
    pay_detail1 = fields.Char(string="Pay Detail 1", size=30)
    pay_detail2 = fields.Char(string="Pay Detail 2", size=30)
    pay_detail3 = fields.Char(string="Pay Detail 3", size=30)
    pay_notes = fields.Text(string="Pay Notes")
    pay_type1 = fields.Char(string="Pay Type 1", size=15)
    pay_type2 = fields.Char(string="Pay Type 2", size=15)
    pay_type3 = fields.Char(string="Pay Type 3", size=15)

    stairbiz_job_id = fields.Many2one(
        string="Stairbiz Quotes Job",
        comodel_name="stairbiz_quotes.job",
        compute="_compute_stairbiz_job_id",
        store=True,
    )
    stairbiz_quote_options_attachment_id = fields.Many2one(
        "ir.attachment",
        domain="[('mimetype', 'in', ['application/pdf'])]",
        string="Stairbiz Quote Options",
    )
    stairbiz_quote_options_attachment_datas = fields.Binary(
        related_sudo=True,
        readonly=False,
        prefetch=False,
        compute="_compute_stairbiz_quote_options_attachment_datas",
        inverse="_inverse_stairbiz_quote_options_attachment_datas",
    )
    stairbiz_quote_options_attachment_id_name = fields.Char(
        related="stairbiz_quote_options_attachment_id.name",
    )

    @api.depends(
        "stairbiz_quote_options_attachment_id",
        "stairbiz_quote_options_attachment_id.raw",
    )
    def _compute_stairbiz_quote_options_attachment_datas(self):
        for stairbiz in self:
            stairbiz.stairbiz_quote_options_attachment_datas = base64.b64encode(
                stairbiz.stairbiz_quote_options_attachment_id.raw or b""
            )

    def _inverse_stairbiz_quote_options_attachment_datas(self):
        for stairbiz in self:
            stairbiz.stairbiz_quote_options_attachment_id = False

            if stairbiz.stairbiz_quote_options_attachment_datas:
                stairbiz.stairbiz_quote_options_attachment_id = self.env[
                    "ir.attachment"
                ].create(
                    {
                        "name": stairbiz.name,
                        "datas": stairbiz.stairbiz_quote_options_attachment_datas,
                        "res_model": "stairbiz.stairbiz",
                        "res_id": stairbiz.id,
                        "res_field": "stairbiz_quote_options_attachment_id",
                    }
                )


    def update_sale_order_on_creation_airflow(self):
        _logger.info(
            "Updating sale order on creation airflow for sale order ID: %s", self.id
        )
        attachments = self.env["ir.attachment"].search_fetch(
            [
                ("res_model", "=", "stairbiz.stairbiz"),
                ("res_id", "=", self.id),
                ("res_field", "=", "stairbiz_quote_options_attachment_id"),
            ],
            ["id"],
            limit=1,
        )
        self.stairbiz_quote_options_attachment_id = attachments.id

    @api.depends("quote_number")
    def _compute_stairbiz_job_id(self):
        for quote in self:
            quotes_jobs = self.env["stairbiz_quotes.job"].search_fetch(
                [("quote_number", "=", quote.quote_number)],
                ["id"],
                limit=1,
            )
            quote.stairbiz_job_id = quotes_jobs.id

    def get_stairbiz_table_data(self):
        """Return a dictionary of stairbiz field data.

        Returns a dict where keys are field names and values are combined strings
        of the field value, UOM, and grade separated by spaces.
        """
        result = {}

        # First group - existing fields
        field_groups = [
            (
                "raking_brail_style",
                "raking_brail_style_uom",
                "raking_brail_style_grade",
            ),
            ("void_trim", "void_trim_uom", "void_trim_grade"),
            ("level_brail_style", "level_brail_style_uom", "level_brail_style_grade"),
            ("wall_rail_style", "wall_rail_style_uom", "wall_rail_style_grade"),
            ("base_treads", "base_treads_uom", "base_treads_grade"),
            (
                "wall_rail_bracket_style",
                "wall_rail_bracket_style_uom",
                "wall_rail_bracket_style_grade",
            ),
        ]

        # Second group - top fields with modified structure
        top_field_groups = [
            ("total_rise", "total_rise_uom", None),
            ("dispatch_method", None, None),
            ("quoted_from_plan", None, None),
            ("number_of_risers", "number_of_risers_uom", None),
        ]

        # Add quote fields to result
        result["quote_number"] = self.quote_number or ""
        result["quote_date"] = str(self.quote_date) if self.quote_date else ""
        result["quote_price"] = str(self.quote_price) if self.quote_price else ""
        result["quote_representative"] = (
            self.quote_representative.name if self.quote_representative else ""
        )

        # Process existing fields
        for base_field, uom_field, grade_field in field_groups:
            # Get the base value
            base_value = self[base_field] or ""

            # Get UOM name if exists
            uom_value = ""
            if uom_field and self[uom_field]:
                uom_value = self[uom_field].name or ""

            # Get grade name if exists
            grade_value = ""
            if grade_field and self[grade_field]:
                grade_value = self[grade_field].name or ""

            # Combine values with spaces
            combined_value = " ".join(
                filter(None, [base_value, uom_value, grade_value])
            )
            result[base_field] = combined_value

        # Process top fields (these will be empty strings since they don't exist yet)
        for base_field, uom_field, _ in top_field_groups:
            # Get the base value
            base_value = self[base_field]

            # For fields without UOM, just use the base field
            if not uom_field:
                if isinstance(base_value, bool):
                    result[base_field] = str(base_value)
                elif base_field == "dispatch_method" and base_value:
                    result[base_field] = dict(
                        self._fields["dispatch_method"].selection
                    ).get(base_value, "")
                else:
                    result[base_field] = str(base_value) if base_value else ""
            else:
                # For fields with UOM, combine with UOM
                uom_value = self[uom_field].name if self[uom_field] else ""
                combined_value = " ".join(
                    filter(None, [str(base_value) if base_value else "", uom_value])
                )
                result[base_field] = combined_value
        return result

    def _get_selection_label(self, field_name, value):
        return dict(self._fields[field_name].selection).get(value, "N/A")

    def generate_stairbiz_job(self):
        self.ensure_one()

        if self.stairbiz_job_generated:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": "Warning",
                    "message": "StairBiz Job has already been generated for this quote.",
                    "type": "warning",
                },
            }

        # Generate a unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"stairbiz_job_{self.id}_{timestamp}.txt"

        # Prepare the content for the file
        content = f"""
StairBiz Job Details:
--------------------
Template: {self._get_selection_label('stairbiz_template', self.stairbiz_template)}
Status: {self._get_selection_label('stairbiz_status', self.stairbiz_status)}
Project Name: {self.project_name or 'N/A'}
Job Name: {self.job_name or 'N/A'}
Job Number: {self.job_number or 'N/A'}
Client Name: {self.client_name or 'N/A'}
Created By: {self.created_by or 'N/A'}
Job Date: {self.job_date or 'N/A'}
Purchase Order: {self.purchase_order or 'N/A'}
Quote Number: {self.quote_number or 'N/A'}
Sales Person: {self.sales_person or 'N/A'}

Client Details:
--------------
Address: {self.client_street or ''}, {self.client_suburb or ''}, {self.client_state or ''}, {self.client_zip or ''}
Phone: {self.client_phone or 'N/A'}
Email: {self.client_email or 'N/A'}

Site Details:
------------
Address: {self.site_street or ''}, {self.site_city or ''}, {self.site_suburb or ''}, {self.site_state or ''}, {self.site_zip or ''}
Contact: {self.site_contact.name if self.site_contact else 'N/A'}
Phone: {self.site_phone or 'N/A'}
Odoo Site Address ID: {self.site_address_id.id if self.site_address_id else 'N/A'}

Technical Details:
-----------------
Lower Floor: {self._get_selection_label('lower_floor', self.lower_floor)}
Upper Floor: {self._get_selection_label('upper_floor', self.upper_floor)}
Requires CNC: {'Yes' if self.requires_cnc else 'No'}
Schedule CNC: {self.schedule_cnc or 'N/A'}
Schedule Production: {self.schedule_production or 'N/A'}
Schedule Install: {self.schedule_install or 'N/A'}

Financial Details:
-----------------
Quote Discount: {self.quote_discount or 'N/A'}
Profit Percent: {self.profit_percent or 'N/A'}
Tax Percent 1: {self.tax_percent1 or 'N/A'}
Tax Percent 2: {self.tax_percent2 or 'N/A'}

Notes:
-----
    {self.job_note1 or ''}
    {self.job_note2 or ''}
            """

        # Ensure the directory exists
        directory = os.path.expanduser("~/sbiz_data")
        os.makedirs(directory, exist_ok=True)

        # Write the content to the file
        file_path = os.path.join(directory, filename)
        with open(file_path, "w") as f:
            f.write(content)

        # Update the record
        self.write(
            {
                "stairbiz_job_generated": True,
                "stairbiz_job_filename": filename,
            }
        )

        # Also update order_id if exists
        if self.order_id:
            self.order_id.write(
                {
                    "stage_id": self.env.ref(
                        "stairmaster_crm_stairbiz.sale_order_stage_sent_to_stairbiz"
                    ).id,
                }
            )

        # Log the action in the chatter
        if self.env.context.get("skip_message_post"):
            _logger.info(
                "Skipping message_post for StairBiz Job file creation for file %s",
                filename,
            )
            return
        self.message_post(body=f"StairBiz Job file created: {filename}")

        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": "Success",
                "message": f"StairBiz Job file created: {filename}",
                "type": "success",
                "sticky": False,
            },
        }

    def regenerate_stairbiz_job(self):
        self.ensure_one()

        # If a previous file exists, delete it
        if self.stairbiz_job_filename:
            old_file_path = os.path.join("/mnt/sbiz_data", self.stairbiz_job_filename)
            if os.path.exists(old_file_path):
                os.remove(old_file_path)

        # Reset the fields
        self.write({"stairbiz_job_generated": False, "stairbiz_job_filename": False})

        # Generate a new job file
        return self.generate_stairbiz_job()

    def preview_stairbiz_job(self):
        self.ensure_one()

        # Prepare the preview content (similar to the file content in generate_stairbiz_job)
        preview_content = f"""
            StairBiz Job Preview:
            --------------------
            Template: {self._get_selection_label('stairbiz_template', self.stairbiz_template)}
            Status: {self._get_selection_label('stairbiz_status', self.stairbiz_status)}
            Project Name: {self.project_name or 'N/A'}
            Job Name: {self.job_name or 'N/A'}
            Job Number: {self.job_number or 'N/A'}
            Client Name: {self.client_name or 'N/A'}
            Created By: {self.created_by or 'N/A'}
            Job Date: {self.job_date or 'N/A'}

            Client Details:
            --------------
            Address: {self.client_street or ''}, {self.client_suburb or ''}, {self.client_state or ''}, {self.client_zip or ''}
            Phone: {self.client_phone or 'N/A'}
            Email: {self.client_email or 'N/A'}

            Site Details:
            ------------
            Address: {self.site_street or ''}, {self.site_city or ''}, {self.site_suburb or ''}, {self.site_state or ''}, {self.site_zip or ''}
            Contact: {self.site_contact.name if self.site_contact else 'N/A'}
            Phone: {self.site_phone or 'N/A'}

            Technical Details:
            -----------------
            Lower Floor: {self._get_selection_label('lower_floor', self.lower_floor)}
            Upper Floor: {self._get_selection_label('upper_floor', self.upper_floor)}
            Requires CNC: {'Yes' if self.requires_cnc else 'No'}

            Notes:
            -----
            {self.job_note1 or ''}
            {self.job_note2 or ''}
                    """

        return {
            "name": "StairBiz Job Preview",
            "type": "ir.actions.act_window",
            "res_model": "stairbiz.preview.wizard",
            "view_mode": "form",
            "target": "new",
            "context": {"default_preview_content": preview_content},
        }

    def update_site_address_from_site_address(self):
        self.ensure_one()
        _logger.info(
            "==== Begin update_site_address_from_site_address with '%s' ====",
            self.site_street,
        )
        try:
            _logger.info("==== Get first Response: %s ====", self.site_street)
            result = self.sh_get_partial_address(self.site_street)

            _logger.info("==== Response: %s ====", result)

            if not result:
                return
            result = result[0]
            _logger.info("==== Get final Response: %s ====", result)
            final_result = self.sh_fill_address(
                result["description"], result["place_id"]
            )
            if final_result:
                site_address = self.env["res.partner"].create(
                    {
                        "type_contact": "site_addresses",
                        "name": final_result.get("formatted_street", ""),
                        "street": final_result.get("formatted_street", ""),
                        "city": final_result.get("city", ""),
                        "zip": final_result.get("zip", ""),
                        "state_id": final_result.get("state", False),
                    }
                )
                if site_address:
                    self.write(
                        {
                            "site_address_id": site_address.id,
                        }
                    )
                    if self.order_id:
                        self.order_id.write(
                            {
                                "site_address_id": site_address.id,
                            }
                        )
        except Exception as e:
            traceback_str = "".join(traceback.format_tb(e.__traceback__))
            _logger.warning("Error while updating site address: %s", e)
            _logger.warning("Traceback:\n %s", traceback_str)

    # Google API related methods
    def sh_get_partial_address(self, partial_address):
        company = self.env.company
        country_code_list = company.sh_restricted_country_ids.mapped("code")
        if country_code_list:
            result = ["country:" + str(item) + "|" for item in country_code_list]
            test_list = ["".join(result)]

        if (
            company
            and company.sh_is_enable_google_api_key
            and company.sh_google_api_key
        ):
            params = {
                "key": company.sh_google_api_key,
                "fields": "formatted_address,name",
                "inputtype": "textquery",
                "types": "address",
                "input": partial_address,
            }
            if country_code_list and test_list[0]:
                params.update(
                    {
                        "components": test_list[0],
                    }
                )
            try:
                results = requests.get(
                    f"{GOOGLE_MAP_PLACES}/autocomplete/json", params=params, timeout=2.5
                ).json()
            except (TimeoutError, ValueError) as e:
                _logger.warning("Error while fetching partial address: %s", e)
                return []
            results = (
                results.get("predictions", [])
                if results.get("status", False) == "OK"
                else []
            )

            return [
                {"description": result["description"], "place_id": result["place_id"]}
                for result in results
            ]

        return []

    def sh_fill_address(self, address, place_id):
        company = self.env.company
        if (
            address
            and company
            and company.sh_is_enable_google_api_key
            and company.sh_google_api_key
        ):
            params = {
                "key": company.sh_google_api_key,
                "place_id": place_id,
                "fields": "address_component,adr_address",
            }

            try:
                results = requests.get(
                    f"{GOOGLE_MAP_PLACES}/details/json", params=params, timeout=2.5
                ).json()

                html_address = results["result"]["adr_address"]
                results = results["result"]["address_components"]

                for res in results:
                    res["type"] = res.pop("types")[0]

            except (TimeoutError, ValueError):
                return {}

            sequence = list(self.get_google_fields_mapping().keys())
            results.sort(
                key=lambda result: sequence.index(result["type"])
                if result["type"] in sequence
                else 143
            )
            complete_address = self._convert_to_standard_address(results)

            # To avoid missing any type of user-inputted number
            if "number" not in complete_address:
                house_number = (
                    address.replace(complete_address.get("zip", ""), "")
                    .replace(complete_address.get("street", ""), "")
                    .replace(complete_address.get("city", ""), "")
                    .replace("-", "")
                )
                complete_address["number"] = house_number.split(",")[0].strip()
                complete_address[
                    "formatted_street"
                ] = f'{complete_address.get("street", "")} {complete_address["number"]}'
            else:
                html2street = html2plaintext(html_address.split(",")[0])
                street = (
                    f'{complete_address.get("street", "")} {complete_address["number"]}'
                )
                complete_address["formatted_street"] = (
                    html2street if len(html2street) >= len(street) else street
                )
            return complete_address if complete_address else {}
        return {}

    def _convert_to_standard_address(self, g_fields):
        """Convert Google Maps API address fields to standard address fields in Odoo.

        :param g_fields: List of dictionaries with address fields from Google Maps API
        :return: Dictionary with standard address values
        """
        address_vals = {}
        google_fields_mapping = self.get_google_fields_mapping()
        for g_field in g_fields:
            fields_standard = (
                google_fields_mapping[g_field["type"]]
                if g_field["type"] in google_fields_mapping
                else []
            )

            for s_field in fields_standard:
                if s_field in address_vals:
                    continue
                if s_field == "country":
                    country = self.env["res.country"].search(
                        [("code", "=", g_field["short_name"].upper())], limit=1
                    )
                    address_vals[s_field] = country.id if country else False
                    address_vals["country_code"] = country.code if country else False
                elif s_field == "state":
                    domain = [("code", "=", g_field["short_name"].upper())]
                    if address_vals.get("country"):
                        domain = expression.AND(
                            [domain, [("country_id.id", "=", address_vals["country"])]]
                        )
                    state = self.env["res.country.state"].search(domain)
                    if len(state) == 1:
                        address_vals[s_field] = state.id
                else:
                    address_vals[s_field] = g_field["long_name"]
        return address_vals

    def get_google_fields_mapping(self):
        return {
            "country": ["country"],
            "street_number": ["number"],
            "administrative_area_level_3": ["city"],
            "neighborhood": [""],
            "locality": ["city"],
            "route": ["street"],
            "sublocality_level_1": ["street2"],
            "postal_code": ["zip"],
            "administrative_area_level_1": ["state", "city"],
            "administrative_area_level_2": ["state", "country"],
        }

    def action_view_sale_order(self):
        self.ensure_one()
        if not self.order_id:
            return
        return {
            "type": "ir.actions.act_window",
            "res_model": "sale.order",
            "views": [(False, "form")],
            "res_id": self.order_id.id,
            "context": {"create": False},
        }

    @api.model
    def create_sale_order_from_stairbiz_pdf_content(self, file_name):

        # Create sale order with TBA partner
        partner_tba = self.env.ref("stairmaster_contact_extened.tba_partner")
        order = self.create(
            {
                "name": file_name.replace(".pdf", ""),
                "partner_id": partner_tba.id,
                "team_id": self.env.ref(
                    "stairmaster_crm_stairbiz.sale_order_team_stairbiz_quotes"
                ).id,
                "stage_id": self.env.ref(
                    "stairmaster_crm_stairbiz.sale_order_stage_quote_received_stairbiz"
                ).id,
                "quote_request_type_id": self.env.ref(
                    "stairmaster_crm_stairbiz.site_specific_type"
                ).id,
            }
        )

        return order.ids

    def batch_generate_stairbiz_jobs(self, count=None, skip_message_post=True):
        self.ensure_one()

        # Get configurations from system parameters
        IrConfigParam = self.env['ir.config_parameter'].sudo()
        
        # If count not provided, use the value from settings
        if count is None:
            count = int(IrConfigParam.get_param('stairbiz_quotes.entries_to_generate', '100'))
        
        # Validate count
        if count <= 0 or count > 500:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": "Warning",
                    "message": "Please specify a count between 1 and 500.",
                    "type": "warning",
                },
            }

        # Get batch configuration
        batch_name = IrConfigParam.get_param('stairbiz_quotes.batch_name', 'Batch')
        batch_type = IrConfigParam.get_param('stairbiz_quotes.batch_type', 'quote')
        
        # Get current numbers and prefixes based on batch type
        prefix = IrConfigParam.get_param(f'stairbiz_quotes.{batch_type}_prefix', 'Q')
        current_number_param = f'stairbiz_quotes.current_{batch_type}_number'
        current_number = int(IrConfigParam.get_param(current_number_param, '1000'))

        # Get current timestamp for batch identification
        batch_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        batch_id = f"{batch_name}_{batch_timestamp}"

        # Track generated files
        generated_files = []

        for i in range(1, count + 1):
            # Generate unique name and quote number using the configured prefix and incrementing number
            ref_number = current_number + i
            name = f"{batch_name}_{prefix}_{ref_number}"
            quote_number = name  # Use the same value for both name and quote_number

            # Create placeholder record with minimal required fields
            temp_vals = {
                "name": name,
                "quote_number": quote_number,
                "odoo_stairbiz_status": "available",
                "quote_date": fields.Date.today(),
            }
            
            # Call the original generate_stairbiz_job method on the temp record
            temp_record = self.create(temp_vals)
            if skip_message_post:
                # Execute function without returning result
                temp_record.with_context(skip_message_post=True).generate_stairbiz_job()
            else:
                # If not skipping notifications, collect the result
                result = temp_record.generate_stairbiz_job()

            # Add filename to generated files list
            if temp_record.stairbiz_job_filename:
                generated_files.append(temp_record.stairbiz_job_filename)

        # Update the current number in system parameters
        IrConfigParam.set_param(current_number_param, str(current_number + count))

        # Return result without a popup notification if skip_message_post is True
        if skip_message_post:
            # Just return number of files generated
            return len(generated_files)
        else:
            # Return with notification
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": "Success",
                    "message": f"Successfully generated {len(generated_files)} StairBiz job files with batch ID: {batch_id}",
                    "type": "success",
                    "sticky": False,
                },
            }
