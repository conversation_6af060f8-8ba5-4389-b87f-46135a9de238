# -*- coding: utf-8 -*-
from odoo import api, fields, models


class SaleOrder(models.Model):
    _inherit = "sale.order"

    # Fields for controlling team-specific view visibility
    show_designer = fields.<PERSON><PERSON><PERSON>(compute="_compute_team_specific_fields")
    show_job_type = fields.<PERSON><PERSON>an(compute="_compute_team_specific_fields")
    show_documents = fields.<PERSON><PERSON>an(compute="_compute_team_specific_fields")
    is_callups = fields.<PERSON><PERSON><PERSON>(compute="_compute_team_specific_fields", store=True)
    is_warranty = fields.<PERSON><PERSON><PERSON>(compute="_compute_team_specific_fields", store=True)
    is_stairbiz_team = fields.<PERSON><PERSON><PERSON>(compute="_compute_team_specific_fields", store=True)

    @api.depends('team_id')
    def _compute_team_specific_fields(self):
        """Compute fields that control visibility based on sales team"""
        callups_team = self.env.ref('stairmaster_sale.sale_order_team_callups', raise_if_not_found=False)
        warranty_team = self.env.ref('stairmaster_sale.sale_order_team_warranty', raise_if_not_found=False)
        stairbiz_team = self.env.ref('stairmaster_sale.sale_order_team_stairbiz', raise_if_not_found=False)
        
        for record in self:
            # Default all visibility flags to True
            record.show_designer = True
            record.show_job_type = True
            record.show_documents = True
            
            # Set team-specific flags
            record.is_callups = callups_team and record.team_id.id == callups_team.id or False
            record.is_warranty = warranty_team and record.team_id.id == warranty_team.id or False
            record.is_stairbiz_team = stairbiz_team and record.team_id.id == stairbiz_team.id or False
            
            if record.is_callups:
                # Callups: Designer is important, documents less so
                record.show_documents = False
            elif record.is_warranty:
                # Warranty: No need for designer, job type is important
                record.show_designer = False
            elif record.is_stairbiz_team:
                # StairBiz requires everything
                pass
                
    @api.onchange('team_id')
    def _onchange_team_id(self):
        """Set default values when changing team"""
        self.ensure_one()
        
        # Compute team-specific fields
        self._compute_team_specific_fields()
        
        callups_team = self.env.ref('stairmaster_sale.sale_order_team_callups', raise_if_not_found=False)
        warranty_team = self.env.ref('stairmaster_sale.sale_order_team_warranty', raise_if_not_found=False)
        stairbiz_team = self.env.ref('stairmaster_sale.sale_order_team_stairbiz', raise_if_not_found=False)
        
        # Set team-specific defaults
        if callups_team and self.team_id.id == callups_team.id:
            self.sale_type = 'stairs_balustrade'
            self.is_site_specific = True
        elif warranty_team and self.team_id.id == warranty_team.id:
            self.sale_type = 'supply_only'
        elif stairbiz_team and self.team_id.id == stairbiz_team.id:
            self.is_stairbiz = 'yes'
            self.is_measure_sheet = 'yes'
