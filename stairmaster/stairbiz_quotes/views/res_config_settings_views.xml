<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.stairbiz.quotes</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app data-string="StairBiz" string="StairBiz Batch" name="stairbiz_quotes" logo="/stairbiz_quotes/static/description/stairbiz_logo.png">
                    <block title="Batch Configuration" name="batch_config">
                        <div class="col-12 col-lg-6 o_setting_box" id="batch_settings_box">
                            <div class="o_setting_right_pane">
                                <div class="text-muted">
                                    Configure batch processing for StairBiz
                                </div>
                                <div class="content-group" id="batch_settings_group">
                                    <div class="mt16 row">
                                        <label for="batch_name" string="Batch Name" class="col-lg-3 o_light_label"/>
                                        <field name="batch_name" nolabel="1" class="col-lg-9"/>
                                    </div>
                                    <div class="mt16 row">
                                        <label for="batch_type" string="Batch Type" class="col-lg-3 o_light_label"/>
                                        <field name="batch_type" nolabel="1" class="col-lg-9"/>
                                    </div>
                                    <div class="mt16 row">
                                        <label for="entries_to_generate" string="Entries to Generate" class="col-lg-3 o_light_label"/>
                                        <field name="entries_to_generate" nolabel="1" class="col-lg-9"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </block>
                    
                    <block title="Reference Configuration" name="reference_config">
                        <div class="row">
                            <div class="col-12 col-lg-6">
                                <div class="mt16 row">
                                    <label for="quote_prefix" string="Quote Prefix" class="col-lg-3 o_light_label"/>
                                    <field name="quote_prefix" nolabel="1" class="col-lg-9"/>
                                </div>
                                <div class="mt16 row">
                                    <label for="job_prefix" string="Job Prefix" class="col-lg-3 o_light_label"/>
                                    <field name="job_prefix" nolabel="1" class="col-lg-9"/>
                                </div>
                                <div class="mt16 row">
                                    <label for="design_prefix" string="Design Prefix" class="col-lg-3 o_light_label"/>
                                    <field name="design_prefix" nolabel="1" class="col-lg-9"/>
                                </div>
                            </div>
                        </div>
                    </block>
                    
                    <block title="Current Numbers" name="current_numbers">
                        <div class="row">
                            <div class="col-12">
                                <div class="mt16 row">
                                    <label for="current_quote_number" string="Current Quote Number" class="col-lg-3 o_light_label"/>
                                    <field name="current_quote_number" nolabel="1" class="col-lg-9"/>
                                </div>
                                <div class="mt16 row">
                                    <label for="current_job_number" string="Current Job Number" class="col-lg-3 o_light_label"/>
                                    <field name="current_job_number" nolabel="1" class="col-lg-9"/>
                                </div>
                                <div class="mt16 row">
                                    <label for="current_design_number" string="Current Design Number" class="col-lg-3 o_light_label"/>
                                    <field name="current_design_number" nolabel="1" class="col-lg-9"/>
                                </div>
                            </div>
                        </div>
                    </block>
                </app>
            </xpath>
        </field>
    </record>

    <!-- Action to open the batch configuration settings -->
    <record id="action_stairbiz_batch_configuration" model="ir.actions.act_window">
        <field name="name">Batch Configuration</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module': 'stairbiz_quotes'}</field>
    </record>
</odoo>