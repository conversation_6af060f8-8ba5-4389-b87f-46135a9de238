<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_stairbiz_integration_config_form" model="ir.ui.view">
        <field name="name">stairbiz.integration.config.form</field>
        <field name="model">stairbiz.integration.config</field>
        <field name="arch" type="xml">
            <form string="Stairbiz Config">
                <header>
                    <button name="action_test_connection" string="Test Connection" type="object" class="btn-primary"/>
                    <button name="action_check_available_drivers" string="Check Available Drivers" type="object" class="btn-secondary"/>
                    <button name="action_generate_connection_string" string="Generate Connection String" type="object" 
                            invisible="not server or not database"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Configuration Name"/>
                        </h1>
                    </div>
                    <group>
                        <group string="Connection Options">
                            <field name="driver"/>
                            <field name="server" placeholder="e.g. localhost\SQLEXPRESS"/>
                            <field name="port" placeholder="e.g. 1433"/>
                            <field name="database" placeholder="e.g. StairbizDB"/>
                            <field name="username" placeholder="e.g. sa"/>
                            <field name="password" password="True" placeholder="Database password"/>
                        </group>
                        <group string="Advanced">
                            <field name="connection_string" widget="char"/>
                            <field name="active"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_stairbiz_integration_config_tree" model="ir.ui.view">
        <field name="name">stairbiz.integration.config.tree</field>
        <field name="model">stairbiz.integration.config</field>
        <field name="arch" type="xml">
            <list string="Stairbiz Configs">
                <field name="name"/>
                <field name="server"/>
                <field name="database"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <record id="action_stairbiz_integration_config" model="ir.actions.act_window">
        <field name="name">Connection Configurations</field>
        <field name="res_model">stairbiz.integration.config</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>