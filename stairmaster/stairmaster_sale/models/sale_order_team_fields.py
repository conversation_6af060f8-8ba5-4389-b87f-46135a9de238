# -*- coding: utf-8 -*-
from odoo import api, fields, models


class SaleOrderTeamFields(models.Model):
    _inherit = "sale.order"

    # For team-specific behaviors
    show_designer = fields.Boolean(compute="_compute_team_specific_fields", store=False)
    show_job_type = fields.Boolean(compute="_compute_team_specific_fields", store=False)
    
    @api.depends('team_id')
    def _compute_team_specific_fields(self):
        for record in self:
            # Default values
            record.show_designer = True
            record.show_job_type = True
            
            if record.team_id:
                # Get team references
                try:
                    callups_team_id = self.env.ref('stairmaster_sale.sale_order_team_callups').id
                    stairbiz_team_id = self.env.ref('stairmaster_sale.sale_order_team_stairbiz').id
                    warranty_team_id = self.env.ref('stairmaster_sale.sale_order_team_warranty').id
                    
                    team_id = record.team_id.id
                    
                    # Team-specific visibility rules
                    if team_id == callups_team_id:
                        record.show_job_type = False
                    elif team_id == warranty_team_id:
                        record.show_designer = False
                except Exception:
                    # If team references don't exist, use defaults
                    pass

    @api.onchange('team_id')
    def _onchange_team_id(self):
        # Set default values based on team
        if self.team_id:
            try:
                callups_team_id = self.env.ref('stairmaster_sale.sale_order_team_callups').id
                warranty_team_id = self.env.ref('stairmaster_sale.sale_order_team_warranty').id
                stairbiz_team_id = self.env.ref('stairmaster_sale.sale_order_team_stairbiz').id
                
                if self.team_id.id == callups_team_id:
                    # Default values for Callups
                    self.sale_type = 'stairs_balustrade'
                    self.is_site_specific = True
                elif self.team_id.id == warranty_team_id:
                    # Default values for Warranty
                    self.sale_type = 'supply_only'
                elif self.team_id.id == stairbiz_team_id:
                    # Default values for StairBiz
                    self.is_stairbiz = 'yes'
            except Exception:
                # If team references don't exist, do nothing
                pass
