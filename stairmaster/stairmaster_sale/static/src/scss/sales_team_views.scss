/* Custom styles for Sales Team views */

/* Common team form styles */
.o_form_view {
    &.quotes_form_view,
    &.orders_form_view,
    &.callups_form_view,
    &.warranty_form_view,
    &.stairbiz_form_view {
        .o_notebook {
            margin-top: 20px;
        }
        
        /* Make status bar more prominent */
        .o_statusbar_status {
            .o_arrow_button.btn-primary {
                font-weight: bold;
            }
        }
        
        /* Style for selection badges */
        .o_field_widget.o_field_selection_badge .badge {
            border-radius: 4px;
            padding: 8px 12px;
            margin-right: 8px;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        /* Style for radio buttons */
        .o_field_widget.o_field_radio.o_horizontal {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            
            .o_radio_item {
                background-color: #f1f1f1;
                border-radius: 4px;
                padding: 5px 10px;
                margin-right: 0;
                
                &.o_radio_item_selected {
                    background-color: #3c77dd;
                    color: white;
                }
            }
        }
        
        /* Make buttons more prominent */
        .btn-primary {
            padding: 8px 16px;
            font-weight: 500;
        }
    }
}

/* Team-specific styles */
.quotes_form_view {
    /* Distinctive color scheme for Quotes */
    .o_statusbar_status .o_arrow_button.btn-primary {
        background-color: #28a745;
        border-color: #28a745;
    }
    
    .o_field_widget.o_field_selection_badge .badge.active {
        background-color: #28a745;
    }
}

.orders_form_view {
    /* Distinctive color scheme for Orders */
    .o_statusbar_status .o_arrow_button.btn-primary {
        background-color: #0062cc;
        border-color: #0062cc;
    }
    
    .o_field_widget.o_field_selection_badge .badge.active {
        background-color: #0062cc;
    }
}

.callups_form_view {
    /* Distinctive color scheme for Callups */
    .o_statusbar_status .o_arrow_button.btn-primary {
        background-color: #6f42c1;
        border-color: #6f42c1;
    }
    
    .o_field_widget.o_field_selection_badge .badge.active {
        background-color: #6f42c1;
    }
    
    /* Simplified layout for Callups */
    .o_group {
        max-width: 800px;
        margin: 0 auto;
    }
}

.warranty_form_view {
    /* Distinctive color scheme for Warranty */
    .o_statusbar_status .o_arrow_button.btn-primary {
        background-color: #fd7e14;
        border-color: #fd7e14;
    }
    
    .o_field_widget.o_field_selection_badge .badge.active {
        background-color: #fd7e14;
    }
}

.stairbiz_form_view {
    /* Distinctive color scheme for StairBiz */
    .o_statusbar_status .o_arrow_button.btn-primary {
        background-color: #e83e8c;
        border-color: #e83e8c;
    }
    
    .o_field_widget.o_field_selection_badge .badge.active {
        background-color: #e83e8c;
    }
}