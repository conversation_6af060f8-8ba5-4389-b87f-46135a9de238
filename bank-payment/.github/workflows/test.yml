name: tests

on:
  pull_request:
    branches:
      - "18.0*"
  push:
    branches:
      - "18.0"
      - "18.0-ocabot-*"

jobs:
  unreleased-deps:
    runs-on: ubuntu-latest
    name: Detect unreleased dependencies
    steps:
      - uses: actions/checkout@v4
      - run: |
          for reqfile in requirements.txt test-requirements.txt ; do
              if [ -f ${reqfile} ] ; then
                  result=0
                  # reject non-comment lines that contain a / (i.e. URLs, relative paths)
                  grep "^[^#].*/" ${reqfile} || result=$?
                  if [ $result -eq 0 ] ; then
                      echo "Unreleased dependencies found in ${reqfile}."
                      exit 1
                  fi
              fi
          done
  test:
    runs-on: ubuntu-22.04
    container: ${{ matrix.container }}
    name: ${{ matrix.name }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - container: ghcr.io/oca/oca-ci/py3.10-odoo18.0:latest
            name: test with Odoo
          - container: ghcr.io/oca/oca-ci/py3.10-ocb18.0:latest
            name: test with OCB
            makepot: "true"
    services:
      postgres:
        image: postgres:12.0
        env:
          POSTGRES_USER: odoo
          POSTGRES_PASSWORD: odoo
          POSTGRES_DB: odoo
        ports:
          - 5432:5432
    env:
      OCA_ENABLE_CHECKLOG_ODOO: "1"
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Install addons and dependencies
        run: oca_install_addons
      - name: Check licenses
        run: manifestoo -d . check-licenses
      - name: Check development status
        run: manifestoo -d . check-dev-status --default-dev-status=Beta
      - name: Initialize test db
        run: oca_init_test_database
      - name: Run tests
        run: oca_run_tests
      - uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
      - name: Update .pot files
        run: oca_export_and_push_pot https://x-access-token:${{ secrets.GIT_PUSH_TOKEN }}@github.com/${{ github.repository }}
        if: ${{ matrix.makepot == 'true' && github.event_name == 'push' && github.repository_owner == 'OCA' }}
