# -*- coding: utf-8 -*-

from odoo import api, fields, models


class Callups(models.Model):
    _name = "callups.callups"
    _description = "Callups"
    _inherit = ["mail.thread", "mail.activity.mixin"]

    name = fields.Char(string="Callup Name", required=True)
    description = fields.Text(string="Description")
    date = fields.Date(string="Date", default=fields.Date.today)
    order_id = fields.Many2one("sale.order", string="Order")
    partner_id = fields.Many2one("res.partner")
    status = fields.Selection(
        [
            ("draft", "Draft"),
            ("in_progress", "In Progress"),
            ("completed", "Completed"),
        ],
        string="Status",
        default="draft",
    )
    customer_job_number = fields.Char(string=" Customer Job Number")
    site_contact = fields.Many2one("res.partner")
    site_email = fields.Char()

    @api.model
    def message_new(self, msg_dict, custom_values=None):
        custom_values = self.order_id.update_partner_from_email(msg_dict, custom_values)
        return super().message_new(msg_dict, custom_values)

    def update_sale_order_from_callups(self):
        mails = self.message_ids

        for mail in mails:
            self.extract_job_number_from_email(mail)

    def extract_job_number_from_email(self, mail):
        """Extract job number from email title and body.

        Args:
            mail (dict): The email data containing subject and body.

        Returns:
            str: The extracted job number if found, otherwise None.
        """
        customer_job_number = None

        # Extract job number from email subject
        if mail.subject:
            customer_job_number = self.extract_job_number_from_email_subject(mail["subject"])

        # If not found in subject, check the body
        if not customer_job_number and mail.body:
            customer_job_number = self.extract_job_number_from_email_body(mail["body"])

        return customer_job_number

    def extract_job_number_from_email_subject(self, email_subject):
        if not email_subject:
            return None

        document_type = self.env["groq.document.type"].search(
            [
                ("model_name", "=", self._name),
                ("partner_id", "=", self.partner_id.id),
                ("name", "like", "Callups Email"),
            ],
            limit=1,
        )
        groq_document_type_ids = document_type and document_type.ids or []

        groq_vals = {
            "res_model": self._name,
            "res_id": self.id,
            "extracted_content": email_subject,
            "name": f"Extract Callups - {self.id}",
            "groq_document_type_ids": [
                (
                    6,
                    0,
                    groq_document_type_ids,
                )
            ],
        }

        if groq_vals.get("groq_document_type_ids"):
            groq_doc = self.env["groq.document"].create(groq_vals)
            groq_doc.process_document()
        return self.customer_job_number

    def extract_job_number_from_email_body(self, email_body):
        if not email_body:
            return None

        document_type = self.env["groq.document.type"].search(
            [("model_name", "=", self._name), ("name", "=", "Email Callups Body Type")],
            limit=1,
        )
        groq_document_type_ids = document_type and document_type.ids or []

        groq_vals = {
            "res_model": self._name,
            "res_id": self.id,
            "extracted_content": email_body,
            "name": f"Extract Callups Body - {self.id}",
            "groq_document_type_ids": [
                (
                    6,
                    0,
                    groq_document_type_ids,
                )
            ],
        }

        if groq_vals.get("groq_document_type_ids"):
            groq_doc = self.env["groq.document"].create(groq_vals)
            groq_doc.process_document()
        return self.customer_job_number
