<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Orders Form View -->
        <record id="view_sale_order_orders_form" model="ir.ui.view">
            <field name="name">sale.order.orders.form</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="stairmaster_sale.view_order_form_inherit"/>
            <field name="mode">primary</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <!-- Add custom class to form -->
                <form position="attributes">
                    <attribute name="class" add="orders_form_view" separator=" "/>
                </form>

                <!-- Modify Sales Type for Orders -->
                <field name="sale_type" position="attributes">
                    <attribute name="readonly">1</attribute>
                </field>

                <!-- Make Project button more prominent -->
                <xpath expr="//field[@name='project_ids']" position="before">
                    <button name="action_view_project_ids" string="Projects" type="object" class="oe_stat_button" icon="fa-tasks"/>
                </xpath>

                <!-- Organize the tabs for better workflow -->
                <notebook position="replace">
                    <notebook>
                        <page name="order_lines" string="Order Lines">
                            <div invisible="show_order_lines == False">
                            <field name="order_line" widget="section_and_note_one2many">
                                <list class="hide_mfg_line">
                                    <control>
                                        <create string="Add a product"/>
                                    </control>
                                    <field name="sequence" widget="handle"/>
                                    <field name="product_template_id" string="Product" optional="show"/>
                                    <field name="product_id" invisible="1"/>
                                    <field name="name" widget="section_and_note_text"/>
                                    <field name="product_uom_qty" string="Qty"/>
                                    <field name="product_uom" string="UoM" groups="uom.group_uom" optional="show"/>
                                    <field name="price_unit" widget="monetary"/>
                                    <field name="discount" string="Disc (%)" optional="show"/>
                                    <field name="price_subtotal" widget="monetary" string="Subtotal"/>
                                    <field name="price_total" widget="monetary" string="Total"/>
                                    <field name="tax_id" widget="many2many_tags" options="{'no_create': True}" domain="[('company_id', '=', company_id)]" optional="show"/>
                                </list>
                            </field>
                            </div>
                        </page>
                        <page name="document_clasification" string="Document Types">
                            <separator string="Process Document" />
                            <button name="action_extract_documents" type="object" string="Process Documents" class="btn-primary"/>
                            <field name="combined_document_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="attachment_datas" filename="attachment_datas_filename" widget="binary"/>
                                    <field name="attachment_datas_filename" column_invisible="1"/>
                                    <field name="groq_document_type_ids" widget="many2many_tags"/>
                                    <field name="processing_status"/>
                                    <button name="process_document" string="Process" type="object" icon="fa-cog"/>
                                </list>
                            </field>
                        </page>
                        <page name="colour_selections" string="Colour Selections">
                            <div invisible="not show_colour_selections">
                            <group>
                                <group>
                                    <field name="range_option_series"/>
                                    <field name="category"/>
                                    <field name="option"/>
                                    <field name="image"/>
                                </group>
                                <group>
                                    <field name="detail_handrail"/>
                                    <field name="balustrade"/>
                                    <field name="timber_newel_post"/>
                                    <field name="timber_newel_post_toppers"/>
                                </group>
                            </group>
                            </div>
                        </page>
                        <page name="requote" string="ReQuote">
                            <div invisible="not show_requote">
                            <group>
                                <field name="requote_sale_order_id"/>
                            </group>
                            </div>
                        </page>
                        <page name="other_information" string="General Information">
                            <div invisible="not show_general_information">
                            <group>
                                <group name="sales_person">
                                    <field name="user_id" domain="[('share', '=', False)]"/>
                                    <field name="team_id" options="{'no_create': True}"/>
                                </group>
                                <group name="sale_info">
                                    <field name="client_order_ref" invisible="1"/>
                                    <field name="require_payment" invisible="1"/>
                                </group>
                            </group>
                            </div>
                        </page>
                    </notebook>
                </notebook>
            </field>
        </record>

        <!-- Orders Tree/List View -->
        <record id="view_sale_order_orders_tree" model="ir.ui.view">
            <field name="name">sale.order.orders.tree</field>
            <field name="model">sale.order</field>
            <field name="arch" type="xml">
                <list string="Orders">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="job_number"/>
                    <field name="sale_type"/>
                    <field name="stage_id"/>
                    <field name="designer_id" optional="show"/>
                    <field name="project_ids" widget="many2many_tags" optional="show"/>
                    <field name="create_date" optional="show"/>
                </list>
            </field>
        </record>

        <!-- Orders Action -->
        <record id="action_sale_order_orders" model="ir.actions.act_window">
            <field name="name">Orders</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">sale.order</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('team_id', '=', 1)]</field>
            <field name="context">{"default_team_id": 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new Order
                </p>
            </field>
            <field name="view_ids" eval="[
                (5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('stairmaster_sale.view_sale_order_orders_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('stairmaster_sale.view_sale_order_orders_form')}),
            ]" />
        </record>

        <!-- Update the Orders menu item -->
        <record id="stairmaster_sale.menu_orders_job" model="ir.ui.menu">
            <field name="action" ref="action_sale_order_orders"/>
        </record>
    </data>
</odoo>