from odoo import fields, models


# Model for sysdiagrams
class StairBizSysDiagram(models.Model):
    _name = "stairbiz_detailing.sysdiagram"
    _description = "StairBiz Sys Diagram"

    name = fields.Char(string="Name")
    principal_id = fields.Char(string="Principal ID")
    diagram_id = fields.Char(string="Diagram ID")
    version = fields.Char(string="Version")
    definition = fields.Binary(string="Definition")


# Model for tblActGroups
class StairBizActGroup(models.Model):
    _name = "stairbiz_detailing.actgroup"
    _description = "StairBiz Activity Group"

    group_key = fields.Char(string="Group Key")
    group_name = fields.Char(string="Group Name")
    staff_count = fields.Char(string="Staff Count")
    is_install = fields.Boolean(string="Is Install")
    activate_trigger = fields.Char(string="Activate Trigger")
    activate_delay = fields.Float(string="Activate Delay")
    group_row = fields.Char(string="Group Row")
    node_stt = fields.Char(string="Node Start")
    node_end = fields.Char(string="Node End")
    group_sort = fields.Char(string="Group Sort")


# Model for tblActivities
class StairBizActivity(models.Model):
    _name = "stairbiz_detailing.activity"
    _description = "StairBiz Activity"

    act_key = fields.Char(string="Activity Key")
    job_key = fields.Char(string="Job Key")
    act_source = fields.Char(string="Activity Source")
    group_key = fields.Char(string="Group Key")
    act_sort = fields.Char(string="Activity Sort")
    act_name = fields.Char(string="Activity Name")
    budget_mins = fields.Char(string="Budget Minutes")
    budget_amt = fields.Char(string="Budget Amount")
    remote_user = fields.Char(string="Remote User")
    act_qty = fields.Char(string="Activity Quantity")
    time_stt = fields.Datetime(string="Time Start")
    pause_stt = fields.Datetime(string="Pause Start")
    time_end = fields.Datetime(string="Time End")
    pause_time = fields.Char(string="Pause Time")
    tail_time = fields.Float(string="Tail Time")
    act_note = fields.Text(string="Activity Note")
    act_note_add = fields.Text(string="Additional Activity Note")
    alert_user = fields.Char(string="Alert User")
    time_set_mode = fields.Char(string="Time Set Mode")
    act_status = fields.Char(string="Activity Status")
    act_dirty = fields.Boolean(string="Activity Dirty")
    activate_date = fields.Datetime(string="Activate Date")
    sched_stt = fields.Datetime(string="Schedule Start")
    sched_end = fields.Datetime(string="Schedule End")
    ready_date = fields.Datetime(string="Ready Date")


# Model for tblClientContacts
class StairBizClientContact(models.Model):
    _name = "stairbiz_detailing.clientcontact"
    _description = "StairBiz Client Contact"

    contact_key = fields.Char(string="Contact Key")
    client_key = fields.Char(string="Client Key")
    contact_name = fields.Char(string="Contact Name")
    salutation = fields.Char(string="Salutation")
    role = fields.Char(string="Role")
    phone = fields.Char(string="Phone")
    fax = fields.Char(string="Fax")
    mobile = fields.Char(string="Mobile")
    tag = fields.Char(string="Tag")
    email = fields.Char(string="Email")
    user_field = fields.Char(string="User Field")
    contact_note = fields.Text(string="Contact Note")
    status = fields.Char(string="Status")


# Model for tblClients
class StairBizClient(models.Model):
    _name = "stairbiz_detailing.client"
    _description = "StairBiz Client"

    client_key = fields.Char(string="Client Key")
    status = fields.Char(string="Status")
    client_name = fields.Char(string="Client Name")
    contact_name = fields.Char(string="Contact Name")
    salutation = fields.Char(string="Salutation")
    street = fields.Char(string="Street")
    suburb = fields.Char(string="Suburb")
    city = fields.Char(string="City")
    state = fields.Char(string="State")
    zip = fields.Char(string="ZIP")
    referred_by = fields.Char(string="Referred By")
    phone = fields.Char(string="Phone")
    fax = fields.Char(string="Fax")
    mobile = fields.Char(string="Mobile")
    email = fields.Char(string="Email")
    company_number = fields.Char(string="Company Number")
    client_note = fields.Text(string="Client Note")
    is_owner = fields.Boolean(string="Is Owner")
    pay1_percent = fields.Char(string="Pay 1 Percent")
    pay2_percent = fields.Char(string="Pay 2 Percent")
    pay2_days = fields.Char(string="Pay 2 Days")
    terms2 = fields.Char(string="Terms 2")
    pay3_days = fields.Char(string="Pay 3 Days")
    terms3 = fields.Char(string="Terms 3")
    discount = fields.Char(string="Discount")
    tag = fields.Char(string="Tag")
    cust_id = fields.Char(string="Cust ID")
    show_critical = fields.Boolean(string="Show Critical")
    critical_note = fields.Char(string="Critical Note")
    sched_colors = fields.Char(string="Sched Colors")
    def_contact = fields.Char(string="Default Contact")
    blob_related_files = fields.Text(string="Blob Related Files")
    inactive = fields.Boolean(string="Inactive")


# Model for tblBlobsUnique
class StairBizBlobUnique(models.Model):
    _name = "stairbiz_detailing.blobunique"
    _description = "StairBiz Blob Unique"

    blob_key = fields.Char(string="Blob Key")
    job_key = fields.Char(string="Job Key")
    type = fields.Char(string="Type")
    sub_type = fields.Char(string="Sub Type")
    blob = fields.Char(string="Blob")
    contact_key = fields.Char(string="Contact Key")
    client_key = fields.Char(string="Client Key")
    contact_name = fields.Char(string="Contact Name")
    salutation = fields.Char(string="Salutation")
    role = fields.Char(string="Role")
    phone = fields.Char(string="Phone")
    fax = fields.Char(string="Fax")
    mobile = fields.Char(string="Mobile")
    tag = fields.Char(string="Tag")
    email = fields.Char(string="Email")
    user_field = fields.Char(string="User Field")
    contact_note = fields.Text(string="Contact Note")
    status = fields.Char(string="Status")


# Model for tblBlobsShared
class StairBizBlobsShared(models.Model):
    _name = "stairbiz_detailing.blobsshared"
    _description = "StairBiz Blob Shared"

    blob_key = fields.Char(string="BlobKey")
    blob_type = fields.Char(string="BlobType")
    blob_string = fields.Text(string="BlobString")

# Model for tblDetails
class StairBizDetail(models.Model):
    _name = "stairbiz_detailing.detail"
    _description = "StairBiz Detail"

    detail_key = fields.Char(string="Detail Key")
    dispose_date = fields.Datetime(string="Dispose Date")
    dispose_mode = fields.Char(string="Dispose Mode")
    schedule_date = fields.Datetime(string="Schedule Date")
    schedule_row = fields.Char(string="Schedule Row")
    travel_hours = fields.Char(string="Travel Hours")
    upper_floor = fields.Char(string="Upper Floor")
    lower_floor = fields.Char(string="Lower Floor")
    stain = fields.Boolean(string="Stain")
    we_paint = fields.Boolean(string="We Paint")
    lining = fields.Char(string="Lining")
    cupboard = fields.Char(string="Cupboard")
    we_cut_floor = fields.Boolean(string="We Cut Floor")
    we_cut_wall = fields.Boolean(string="We Cut Wall")
    brick_upper = fields.Boolean(string="Brick Upper")
    brick_lower = fields.Boolean(string="Brick Lower")
    power_on = fields.Boolean(string="Power On")
    re_measure = fields.Boolean(string="Re Measure")
    briefing = fields.Boolean(string="Briefing")
    from_plan = fields.Boolean(string="From Plan")
    details_note = fields.Text(string="Details Note")
    travel_dollars = fields.Char(string="Travel Dollars")
    requires_cnc = fields.Boolean(string="Requires CNC")
    cnc_schedule_date = fields.Datetime(string="CNC Schedule Date")
    tread_protect = fields.Boolean(string="Tread Protect")
    sched_border_clr = fields.Char(string="Sched Border Color")
    sched_fill_clr = fields.Char(string="Sched Fill Color")
    sched_text_clr = fields.Char(string="Sched Text Color")
    cnc_done = fields.Boolean(string="CNC Done")
    schedule_date2 = fields.Datetime(string="Schedule Date 2")
    travel_hours_b = fields.Char(string="Travel Hours B")
    travel_dollars_b = fields.Char(string="Travel Dollars B")
    schedule_row2 = fields.Char(string="Schedule Row 2")
    show_critical_note = fields.Boolean(string="Show Critical Note")
    sched_icon_list = fields.Text(string="Sched Icon List")
    calc_weight = fields.Boolean(string="Calc Weight")


# Model for tblDone
class StairBizDone(models.Model):
    _name = "stairbiz_detailing.done"
    _description = "StairBiz Done"

    done_key = fields.Char(string="Done Key")
    client = fields.Char(string="Client")
    site = fields.Char(string="Site")
    details = fields.Char(string="Details")
    comps = fields.Char(string="Components")
    setout = fields.Char(string="Setout")
    design = fields.Char(string="Design")
    materials = fields.Char(string="Materials")
    labor = fields.Char(string="Labor")
    quotecalc = fields.Char(string="Quote Calculation")
    quote = fields.Char(string="Quote")
    schedule = fields.Char(string="Schedule")
    invoice = fields.Char(string="Invoice")
    payment = fields.Char(string="Payment")
    receipt = fields.Char(string="Receipt")
    my_data = fields.Char(string="My Data")
    notes = fields.Char(string="Notes")


# Model for tblInvent
class StairBizInventory(models.Model):
    _name = "stairbiz_detailing.inventory"
    _description = "StairBiz Inventory"

    invent_key = fields.Char(string="Inventory Key")
    job_key = fields.Char(string="Job Key")
    type = fields.Char(string="Type")
    loose_type = fields.Char(string="Loose Type")
    category = fields.Char(string="Category")
    style_name = fields.Char(string="Style Name")
    timber = fields.Char(string="Timber")
    description = fields.Char(string="Description")
    width = fields.Char(string="Width")
    depth = fields.Char(string="Depth")
    qty = fields.Char(string="Quantity")
    length = fields.Char(string="Length")
    cost = fields.Char(string="Cost")
    uom_is_each = fields.Boolean(string="UOM Is Each")
    tag = fields.Char(string="Tag")
    total_cost = fields.Char(string="Total Cost")
    waste = fields.Char(string="Waste")
    width_raw = fields.Char(string="Width Raw")
    invent_note = fields.Char(string="Inventory Note")


# Model for tblJobFolders
class StairBizJobFolder(models.Model):
    _name = "stairbiz_detailing.jobfolder"
    _description = "StairBiz Job Folder"

    folder_key = fields.Char(string="Folder Key")
    folder_name = fields.Char(string="Folder Name")


# Model for tblJobLabor
class StairBizJobLabor(models.Model):
    _name = "stairbiz_detailing.joblabor"
    _description = "StairBiz Job Labor"

    invent_key = fields.Char(string="Inventory Key")
    job_key = fields.Char(string="Job Key")
    caption = fields.Char(string="Caption")
    category = fields.Char(string="Category")
    ref = fields.Char(string="Reference")
    stage = fields.Char(string="Stage")
    cost_method = fields.Char(string="Cost Method")
    lab_time = fields.Char(string="Labor Time")
    rate = fields.Char(string="Rate")
    uom_is_each = fields.Boolean(string="UOM Is Each")
    qty = fields.Char(string="Quantity")


# Model for tblJobs
class StairBizJob(models.Model):
    _name = "stairbiz_detailing.job"
    _description = "StairBiz Job"

    job_key = fields.Char(string="Job Key")
    project_key = fields.Char(string="Project Key")
    job_name = fields.Char(string="Job Name")
    directory_note = fields.Char(string="Directory Note")
    job_note = fields.Text(string="Job Note")
    quote_number = fields.Char(string="Quote Number")
    job_number = fields.Char(string="Job Number")
    job_date = fields.Datetime(string="Job Date")
    job_status = fields.Char(string="Job Status")
    save_materials = fields.Boolean(string="Save Materials")
    created_date = fields.Datetime(string="Created Date")
    created_by = fields.Char(string="Created By")
    job_version = fields.Char(string="Job Version")
    site_key = fields.Char(string="Site Key")
    detail_key = fields.Char(string="Detail Key")
    my_data_key = fields.Char(string="My Data Key")
    comp_wnd_key = fields.Char(string="Comp Wnd Key")
    setout_key = fields.Char(string="Setout Key")
    lab_cost_key = fields.Char(string="Lab Cost Key")
    letter_key = fields.Char(string="Letter Key")
    quote_key = fields.Char(string="Quote Key")
    snaps3d_key = fields.Char(string="Snaps3D Key")
    design_key = fields.Char(string="Design Key")
    draws_key = fields.Char(string="Draws Key")
    annotations_key = fields.Char(string="Annotations Key")
    overrides_key = fields.Char(string="Overrides Key")
    scenario_name = fields.Char(string="Scenario Name")
    scenario_id = fields.Char(string="Scenario ID")
    active_scenario = fields.Boolean(string="Active Scenario")
    locked_date = fields.Datetime(string="Locked Date")
    locked_by = fields.Char(string="Locked By")
    payments_key = fields.Char(string="Payments Key")
    done_key = fields.Char(string="Done Key")
    active_mask = fields.Char(string="Active Mask")
    offline_status = fields.Char(string="Offline Status")
    alerts_current = fields.Boolean(string="Alerts Current")
    export_history = fields.Text(string="Export History")
    flag_color = fields.Char(string="Flag Color")
    flag_user = fields.Char(string="Flag User")
    flag_date = fields.Datetime(string="Flag Date")
    flag_notes = fields.Text(string="Flag Notes")
    job_color = fields.Char(string="Job Color")
    sales_person = fields.Char(string="Sales Person")
    purchase_order = fields.Char(string="Purchase Order")
    split_quote = fields.Boolean(string="Split Quote")
    quote2_key = fields.Char(string="Quote 2 Key")
    payments2_key = fields.Char(string="Payments 2 Key")
    contact_key = fields.Char(string="Contact Key")
    all_scenarios_active = fields.Boolean(string="All Scenarios Active")
    last_modified_date = fields.Datetime(string="Last Modified Date")
    quote_active_key = fields.Char(string="Quote Active Key")
    payments_active_key = fields.Char(string="Payments Active Key")
    count_units = fields.Char(string="Count Units")
    used_cnc = fields.Boolean(string="Used CNC")
    cnc_folder_path = fields.Text(string="CNC Folder Path")


# Model for tblMyDataV2Fields
class StairBizMyDataV2Field(models.Model):
    _name = "stairbiz_detailing.mydatafield"
    _description = "StairBiz MyDataV2 Field"

    field_key = fields.Char(string="Field Key")
    name = fields.Char(string="Name")
    settings = fields.Text(string="Settings")
    deleted = fields.Boolean(string="Deleted")
    mmodified = fields.Datetime(string="Modified")


# Model for tblMyDataV2Values
class StairBizMyDataV2Value(models.Model):
    _name = "stairbiz_detailing.mydatafieldvalue"
    _description = "StairBiz MyDataV2 Value"

    group_key = fields.Char(string="Group Key")
    name = fields.Char(string="Name")
    field_key = fields.Char(string="Field Key")
    field_value = fields.Text(string="Field Value")
    field_value_date = fields.Datetime(string="Field Value Date")


# Model for tblNotes
class StairBizNote(models.Model):
    _name = "stairbiz_detailing.note"
    _description = "StairBiz Note"

    notes_key = fields.Char(string="Notes Key")
    job_key = fields.Char(string="Job Key")
    user_key = fields.Char(string="User Key")
    date_and_time = fields.Datetime(string="Date and Time")
    note_text = fields.Text(string="Note Text")


# Model for tblPayments
class StairBizPayment(models.Model):
    _name = "stairbiz_detailing.payment"
    _description = "StairBiz Payment"

    payments_key = fields.Char(string="Payments Key")
    paid_amt1 = fields.Char(string="Paid Amount 1")
    paid_amt2 = fields.Char(string="Paid Amount 2")
    paid_amt3 = fields.Char(string="Paid Amount 3")
    paid_date1 = fields.Datetime(string="Paid Date 1")
    paid_date2 = fields.Datetime(string="Paid Date 2")
    paid_date3 = fields.Datetime(string="Paid Date 3")
    pay_type1 = fields.Char(string="Payment Type 1")
    pay_type2 = fields.Char(string="Payment Type 2")
    pay_type3 = fields.Char(string="Payment Type 3")
    pay_detail1 = fields.Char(string="Payment Detail 1")
    pay_detail2 = fields.Char(string="Payment Detail 2")
    pay_detail3 = fields.Char(string="Payment Detail 3")
    pay_notes = fields.Text(string="Payment Notes")
    for_balust = fields.Boolean(string="For Balust")


# Model for tblProject
class StairBizProject(models.Model):
    _name = "stairbiz_detailing.project"
    _description = "StairBiz Project"

    project_key = fields.Char(string="Project Key")
    project_name = fields.Char(string="Project Name")
    project_is_template = fields.Boolean(string="Project Is Template")
    client_key = fields.Char(string="Client Key")
    save_type = fields.Char(string="Save Type")
    jobs_in_project = fields.Char(string="Jobs in Project")
    folder_key = fields.Char(string="Folder Key")
    shared_detail_key = fields.Char(string="Shared Detail Key")
    shared_comp_wnd_key = fields.Char(string="Shared Comp Wnd Key")
    shared_setout_key = fields.Char(string="Shared Setout Key")
    shared_lab_cost_key = fields.Char(string="Shared Lab Cost Key")
    shared_site_key = fields.Char(string="Shared Site Key")
    shared_my_data_key = fields.Char(string="Shared My Data Key")
    shared_letter_key = fields.Char(string="Shared Letter Key")
    shared_contact_key = fields.Char(string="Shared Contact Key")


# Model for tblQuotations
class StairBizQuotation(models.Model):
    _name = "stairbiz_detailing.quotation"
    _description = "StairBiz Quotation"

    quote_key = fields.Char(string="Quote Key")
    mat_net = fields.Char(string="Material Net")
    mat_load = fields.Char(string="Material Load")
    lab_net = fields.Char(string="Labor Net")
    lab_load = fields.Char(string="Labor Load")
    lab_prep_mins = fields.Char(string="Labor Preparation Minutes")
    lab_turn_mins = fields.Char(string="Labor Turn Minutes")
    lab_cons_mins = fields.Char(string="Labor Construction Minutes")
    lab_inst_mins = fields.Char(string="Labor Installation Minutes")
    mat_total = fields.Char(string="Material Total")
    lab_total = fields.Char(string="Labor Total")
    truck_total = fields.Char(string="Truck Total")
    ohead_total = fields.Char(string="Overhead Total")
    net_plus_profit = fields.Char(string="Net Plus Profit")
    tax_percent1 = fields.Float(string="Tax Percent 1")
    grand_total = fields.Char(string="Grand Total")
    freeze = fields.Boolean(string="Freeze")
    lab_mat_pw = fields.Char(string="Labor Material Price Weighted")
    ohead_pw = fields.Char(string="Overhead Price Weighted")
    due_amt1 = fields.Char(string="Due Amount 1")
    due_amt2 = fields.Char(string="Due Amount 2")
    profit_percent = fields.Float(string="Profit Percent")
    quantity = fields.Char(string="Quantity")
    discount = fields.Float(string="Discount")
    tax_percent2 = fields.Float(string="Tax Percent 2")
    lab_delv_mins = fields.Char(string="Labor Delivery Minutes")
    notes = fields.Text(string="Notes")
    deliver_net = fields.Char(string="Deliver Net")
    install_net = fields.Char(string="Install Net")
    profit_adjust = fields.Char(string="Profit Adjust")
    price = fields.Char(string="Price")
    discount_amt = fields.Char(string="Discount Amount")
    tax_amount1 = fields.Char(string="Tax Amount 1")
    tax_amount2 = fields.Char(string="Tax Amount 2")
    net_profit = fields.Char(string="Net Profit")
    parts_lock = fields.Char(string="Parts Lock")
    line_items_lock = fields.Char(string="Line Items Lock")
    timber_lock = fields.Char(string="Timber Lock")
    build_lock = fields.Char(string="Build Lock")
    deliver_lock = fields.Char(string="Deliver Lock")
    install_lock = fields.Char(string="Install Lock")
    due_perc1 = fields.Float(string="Due Percent 1")
    due_perc2 = fields.Float(string="Due Percent 2")
    due_amt3 = fields.Char(string="Due Amount 3")
    for_balust = fields.Boolean(string="For Balust")
    ohead_mat_factor = fields.Char(string="Overhead Material Factor")
    lab_cnc_mins = fields.Char(string="Labor CNC Minutes")
    add_discount = fields.Boolean(string="Add Discount")
    balance_due = fields.Char(string="Balance Due")
    quick_install_mins = fields.Char(string="Quick Install Minutes")
    quick_install_doll = fields.Char(string="Quick Install Dollars")
    quick_install_override = fields.Boolean(string="Quick Install Override")


# Model for tblSharedConfig
class StairBizSharedConfig(models.Model):
    _name = "stairbiz_detailing.sharedconfig"
    _description = "StairBiz Shared Config"

    config_id = fields.Char(string="Config ID")
    index_num = fields.Char(string="Index Number")
    modified = fields.Datetime(string="Modified")
    config_blob = fields.Text(string="Config Blob")


# Model for tblSharedConfigCtl
class StairBizSharedConfigCtl(models.Model):
    _name = "stairbiz_detailing.sharedconfigctl"
    _description = "StairBiz Shared Config Control"

    pk = fields.Char(string="Primary Key")
    modified = fields.Datetime(string="Modified")


# Model for tblSites
class StairBizSite(models.Model):
    _name = "stairbiz_detailing.site"
    _description = "StairBiz Site"

    site_key = fields.Char(string="Site Key")
    measure_date = fields.Datetime(string="Measure Date")
    site_contact = fields.Char(string="Site Contact")
    street = fields.Char(string="Street")
    suburb = fields.Char(string="Suburb")
    city = fields.Char(string="City")
    state = fields.Char(string="State")
    zip = fields.Char(string="ZIP")
    map_ref = fields.Char(string="Map Reference")
    cross_street = fields.Char(string="Cross Street")
    phone = fields.Char(string="Phone")
    is_new_home = fields.Boolean(string="Is New Home")
    measure_note = fields.Text(string="Measure Note")
    mobile = fields.Char(string="Mobile")
    site_note = fields.Text(string="Site Note")
    build_code = fields.Char(string="Build Code")
    site_email = fields.Char(string="Site Email")


# Model for tblSNUA
class StairBizSNUA(models.Model):
    _name = "stairbiz_detailing.snua"
    _description = "StairBiz SNUA"

    last_snua = fields.Char(string="Last SNUA")


# Model for tblUsers
class StairBizUser(models.Model):
    _name = "stairbiz_detailing.user"
    _description = "StairBiz User"

    user_key = fields.Char(string="User Key")
    snua = fields.Char(string="SNUA")
    user_name = fields.Char(string="User Name")
    user_password = fields.Char(string="User Password")
    full_name = fields.Char(string="Full Name")
    phone = fields.Char(string="Phone")
    email = fields.Char(string="Email")
    needs_save_to_server = fields.Boolean(string="Needs Save to Server")
    deleted = fields.Boolean(string="Deleted")
    permissions = fields.Text(string="Permissions")
    group_key = fields.Char(string="Group Key")
    is_a_group_header = fields.Boolean(string="Is a Group Header")
    hide = fields.Boolean(string="Hide")


# Model for tblVersion
class StairBizVersion(models.Model):
    _name = "stairbiz_detailing.version"
    _description = "StairBiz Version"

    pk = fields.Char(string="Primary Key")
    ver_date = fields.Datetime(string="Version Date")
    version = fields.Char(string="Version")
