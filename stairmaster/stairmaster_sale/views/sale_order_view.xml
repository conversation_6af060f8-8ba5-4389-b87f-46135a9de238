<?xml version ="1.0" encoding="utf-8"?>
<odoo>

    <menuitem
        id="job_menu"
        name="Sales"
        parent="crm.crm_menu_root"
        sequence="1"/>

    <record id="crm.crm_menu_sales" model="ir.ui.menu">
        <field name="active" eval="False"/>
    </record>

    <menuitem
        id="crm.crm_menu_root"
        name="Job Entry"
        action="sales_team.crm_team_action_pipeline"
        sequence="1"/>

    <record id="view_order_tree_inherit" model="ir.ui.view">
        <field name="name">view.order.tree.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.sale_order_tree"/>
        <field name="arch" type="xml">
            <field name="partner_id" position="after">
                <field name="sale_type" readonly="1" optional="show"/>
                <field name="quote_request_type_id" optional="show"/>
            </field>
        </field>
    </record>

    <record id="stairmaster_view_order_tree" model="ir.ui.view">
        <field name="name">stairmaster.sale.order.tree</field>
        <field name="model">sale.order</field>
        <field name="arch" type="xml">
            <list string="Sales Orders">
                <field name="name"/>
                <field name="designer_id"/>
                <field name="sale_type"/>
                <field name="partner_id"/>
                <field name="team_id" string="Created from"/>
                <field name="customer_job_number"/>
            </list>
        </field>
    </record>

    <record id="view_order_form_inherit" model="ir.ui.view">
        <field name="name">sale.order.form.view.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <field name="state" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <xpath expr="//header" position="inside">
                <field name="stage_id" widget="statusbar" options="{'clickable': '1', 'fold_field': 'fold'}" domain="[('team_id', '=', team_id)]" can_write="True"/>
            </xpath>

            <xpath expr="//field[@name='sale_order_template_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//button[@id='send_by_email_primary']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@id='send_by_email']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='name']/parent::h1" position="replace">
                <h3 style='margin-bottom: 15px;'>
                    <field name="name" readonly="1"/>
                </h3>
            </xpath>
            <xpath expr="//form" position="inside">
                <group name="quote_request_type" invisible="1">
                    <field name="show_general_information"/>
                    <field name="show_requote"/>
                    <field name="show_order_lines"/>
                    <field name="show_colour_selections"/>
                </group>
            </xpath>
            <field name="validity_date" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="pricelist_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="tag_ids" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="payment_term_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <group name="partner_details" position="inside">
                <field name="sale_type" />
                <field name="quote_request_type_id" string="Job Type"/>
                <field name="designer_id" />
                <script type="text/javascript" src="/stairmaster_sale/static/src/js/hide_mfg_line.js"/>
            </group>
            <field name="payment_term_id" position="after">
                <field name="job_number" />
                <field name="customer_job_number" />
            </field>
            <!-- Order Lines Page -->
            <xpath expr="//page[@name='order_lines']" position="inside">
                <div invisible="show_order_lines"></div>
            </xpath>
            <!-- Other Information page -->
            <xpath expr="//page[@name='other_information']" position="attributes">
                <attribute name="string">General Information</attribute>
            </xpath>
            <xpath expr="//page[@name='other_information']" position="inside">
                <div invisible="show_general_information"></div>
            </xpath>
            <xpath expr="//field[@name='payment_term_id']" position="after">
                <xpath expr="//page[@name='other_information']//group[@name='sales_person']/field[@name='team_id']" position="move" />
            </xpath>
            <group name="sale_reporting" position="attributes">
                <attribute name="invisible">1</attribute>
            </group>
            <xpath expr="//group[@name='sales_person']/field[@name='tag_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <field name="client_order_ref" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="require_payment" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <xpath expr="//group[@name='partner_details']/parent::group" position="after">
                <group>
                    <group>
                        <!-- <div colspan="2" style="display:flex; align-items: baseline">
                            <label for="stair_calculator_id" style="opacity:1.0 !important" string="Stair Calculator" />
                            <span style="margin-left: 20px">
                                <button name="action_open_stair_calculator" type="object" class="btn-secondary" string="Create Stair Calculator" invisible="stair_calculator_id" />
                                <field name="stair_calculator_id" invisible="not stair_calculator_id" option="{'autosave': False}" context="{'default_sale_order_id': active_id}" domain="[('sale_order_id','=','active_id')]" readonly="1"/>
                            </span>
                        </div> -->
                    </group>
                    <group>
                    </group>
                </group>
            </xpath>
            <xpath expr="//sheet/notebook/page[@name='order_lines']/field[@name='order_line']/list" position="attributes">
                <attribute name="class" add="hide_mfg_line" separator=" "/>
            </xpath>

            <!-- <xpath expr="//group[@name='site_address']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath> -->
            <xpath expr="//page[@name='other_information']//group[@name='sale_shipping']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='optional_products']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='customer_signature']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page name="document_clasification" string="Documents">
                    <separator string="Process Document" />
                    <button name="action_extract_documents" type="object" string="Process Documents" class="btn-primary"/>
                    <field name="combined_document_ids">
                        <list>
                            <field name="name"/>
                            <field name="attachment_datas" filename="attachment_datas_filename" widget="binary"/>
                            <field name="attachment_datas_filename" column_invisible="1"/>
                            <field name="groq_document_type_ids" widget="many2many_tags"/>
                            <field name="processing_status" readonly="1"/>
                            <button name="process_document" string="Process" type="object" icon="fa-cog"/>
                        </list>
                        <form>
                            <sheet>
                                <group>
                                    <field name="name"/>
                                    <field name="groq_document_type_ids" widget="many2many_tags" options="{'no_create': True}"/>
                                    <field name="processing_status"/>
                                    <field name="attachment_id"/>
                                    <field name="partner_id" domain="[('type_contact','=','builders')]"/>
                                </group>
                                <notebook>
                                    <page string="Extracted Content">
                                        <field name="returned_text" widget="ace" style="height: 600px; font-family: monospace; white-space: pre-wrap; word-wrap: break-word;"/>
                                    </page>
                                    <page string="PDF Preview">
                                        <field name="attachment_datas" widget="pdf_viewer"/>
                                    </page>
                                    <page string="Processing Log">
                                        <field name="processing_log"/>
                                    </page>
                                    <page string="Extracting Log">
                                        <field name="extract_logs"/>
                                    </page>
                                </notebook>
                            </sheet>
                        </form>
                    </field>
                </page>
                <page name="requote" class="standout-tab" string="ReQuote">
                    <div invisible="not show_requote">
                        <group>
                            <field name="requote_sale_order_id"/>
                        </group>
                    </div>
                </page>

                <page name="colour_selections" string="Colour Selections" invisible="not show_colour_selections">
                    <div>
                        <group>
                            <group>
                                <field name="range_option_series"/>
                                <field name="category"/>
                                <field name="option"/>
                                <field name="image"/>
                            </group>
                            <group>
                            </group>
                        </group>
                        <group>
                            <group>
                                <field name="detail_handrail"/>
                                <field name="handrail_finish"/>
                                <field name="handrail_style"/>
                                <field name="handrail_colour"/>
                                <field name="handrail_application"/>
                            </group>
                            <group>
                                <field name="balustrade"/>
                                <field name="bracket_colour"/>
                                <field name="timber_newel_post"/>
                                <field name="timber_newel_post_toppers"/>
                            </group>
                        </group>
                    </div>
                </page>
                <xpath expr="//page[@name='other_information']" position="move" />
            </xpath>
        </field>
    </record>

    <record id="action_stairmaster_sales_quotes" model="ir.actions.act_window">
        <field name="name">View Quotes</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">list,form</field>
        <field name="res_model">sale.order</field>
        <field name="context">{'skip_compute_data': True, 'default_is_quote': True}</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (ref('stairmaster_sale.stairmaster_view_order_tree'), 'list'),
            (ref('stairmaster_sale.view_order_form_inherit'), 'form')]"/>
        <field name="view_ids" eval="[
                (5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('stairmaster_sale.stairmaster_view_order_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('stairmaster_sale.view_order_form_inherit')}),
            ]" />
        <field name="domain" eval="[('team_id', 'in', [ref('stairmaster_sale.sale_order_team_quotes')])]" />
    </record>

    <record id="action_stairmaster_sales_orders" model="ir.actions.act_window">
        <field name="name">View Orders</field>
        <field name="type">ir.actions.act_window</field>
        <field name="view_mode">list,form</field>
        <field name="res_model">sale.order</field>
        <field name="context">{'skip_compute_data': True, 'default_is_order': True}</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (ref('stairmaster_sale.stairmaster_view_order_tree'), 'list'),
            (ref('stairmaster_sale.view_order_form_inherit'), 'form')]"/>
        <field name="view_ids" eval="[
                (5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('stairmaster_sale.stairmaster_view_order_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('stairmaster_sale.view_order_form_inherit')}),
            ]" />
        
        <field name="domain" eval="[('team_id', 'in', [ref('stairmaster_sale.sale_order_team_orders')])]" />
    </record>

    <record id="sales_team.crm_team_action_pipeline" model="ir.actions.act_window">
        <field name="name">Teams</field>
        <field name="res_model">crm.team</field>
        <field name="view_mode">kanban,form</field>
        <field name="view_ids" eval="[(5, 0, 0),
            (ref('stairmaster_sale.view_crm_team_kanban_dashboard_inherit'), 'kanban')]"/>
    </record>

    <menuitem
        id="menu_orders_job"
        name="Orders"
        parent="job_menu"
        action="action_stairmaster_sales_orders"
        sequence="1"/>

    <menuitem
        id="menu_quotes_job"
        name="Quotes"
        action="action_stairmaster_sales_quotes"
        parent="job_menu"
        sequence="2"/>

    <menuitem
        id="menu_callups_job"
        name="Callups"
        action="stairmaster_sale.action_sale_order_callups"
        parent="job_menu"
        sequence="4"/>

    <menuitem
        id="menu_teams"
        name="Teams"
        action="sales_team.crm_team_action_config"
        parent="job_menu"
        sequence="4"/>

</odoo>
