import ast
import base64
import json
import logging
import os
import re
import traceback
from datetime import datetime, timedelta
from email.message import EmailMessage

import markdownify
import requests
from markupsafe import Markup
from odoo import api, fields, models
from odoo.exceptions import ValidationError
from odoo.osv import expression
from odoo.tools import html2plaintext

_logger = logging.getLogger(__name__)


GOOGLE_MAP_PLACES = "https://maps.googleapis.com/maps/api/place"
GOOGLE_DIRECTIONS = "https://maps.googleapis.com/maps/api/directions"


def download_and_convert_to_base64(image_url):
    try:
        # Download image
        response = requests.get(image_url, timeout=10)
        if response.status_code == 200:
            # Convert to Base64
            image_data = base64.b64encode(response.content).decode("utf-8")
            # Extract MIME type (assume PNG if unknown)
            mime_type = response.headers.get("Content-Type", "image/png")
            return f"data:{mime_type};base64,{image_data}"
        else:
            print(f"Failed to download image: {image_url}")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None


def replace_image_src(html_str):
    # Regex to find all <img> tags
    img_pattern = r'<img[^>]+src="(https://ci3.googleusercontent\.com/[^"]+)"'

    def replace_match(match):
        image_url = match.group(1)
        base64_src = download_and_convert_to_base64(image_url)
        if base64_src:
            return match.group(0).replace(image_url, base64_src)
        return match.group(0)

    # Replace all matches in HTML
    updated_html = re.sub(img_pattern, replace_match, html_str)
    return updated_html


def refine_email_content(email_content):
    if type(email_content) == str:
        # email_content = ast.literal_eval(email_content)
        email_content = json.loads(email_content)
    if email_content.get("body"):
        email_content["body"] = replace_image_src(email_content["body"])
        email_content["body"] = Markup(email_content["body"])
    if email_content.get("date"):
        dt = datetime.strptime(email_content.get("date"), "%a, %d %b %Y %H:%M:%S %z")
        email_content["date"] = dt.strftime("%Y-%m-%d %H:%M:%S")
    return email_content


class SaleOrder(models.Model):
    _inherit = "sale.order"

    stairbiz_ids = fields.Many2many(
        "stairbiz.stairbiz",
        relation="stairbiz_stairbiz_sale_order_rel",
        domain="['|', ('order_id', '=', False), ('order_id', '=', id)]",
        string="StairBiz Quote",
    )

    stairbiz_job_generated = fields.Boolean(
        string="StairBiz Job Generated", default=False
    )
    stairbiz_quote_count = fields.Integer(
        string="StairBiz Quote Count", compute="_compute_stairbiz_quote_count"
    )
    @api.depends("stairbiz_ids")
    def _compute_stairbiz_quote_count(self):
        for sale in self:
            sale.stairbiz_quote_count = len(sale.stairbiz_ids)

    def action_stairmaster_view_stairbiz_quotes(self):
        self.ensure_one()
        return {
            "type": "ir.actions.act_window",
            "name": "StairBiz Quotes",
            "res_model": "stairbiz.stairbiz",
            "domain": [("id", "in", self.stairbiz_ids.ids)],
            "view_mode": "list,form",
            'view_ids': [
                (self.env.ref("stairmaster_crm_stairbiz.view_stairbiz_tree").id, 'list'),
                (False, 'form'),
            ],
        }

    site_state = fields.Char(string="State", size=10)
    site_street = fields.Char(string="Street", size=35, tracking=True)
    site_suburb = fields.Char(string="Suburb", size=20)
    site_zip = fields.Char(string="Zip", size=8)
    site_country = fields.Char(string="Country", size=20)
    site_city = fields.Char(string="City", size=20)

    # Display options
    show_stairbiz_detail = fields.Boolean(compute="_compute_show_stairbiz_detail")

    # Quote Request Type related fields
    show_document_type = fields.Boolean(compute="_compute_on_quote_request_type_id")
    show_site_address = fields.Boolean(compute="_compute_on_quote_request_type_id")
    show_site_contact = fields.Boolean(compute="_compute_on_quote_request_type_id")
    show_stairbiz_detail = fields.Boolean(compute="_compute_show_stairbiz_detail")
    show_project_detail = fields.Boolean(compute="_compute_on_quote_request_type_id")
    show_job_detail = fields.Boolean(compute="_compute_on_quote_request_type_id")

    @api.depends("team_id", "quote_request_type_id")
    def _compute_show_stairbiz_detail(self):
        order_team = self.env.ref("stairmaster_sale.sale_order_team_orders")
        for sale in self:
            if any(
                [
                    sale.team_id == order_team,
                    not sale.quote_request_type_id.show_stairbiz_detail,
                ]
            ):
                sale.show_stairbiz_detail = False
            else:
                sale.show_stairbiz_detail = True

    @api.onchange("requote_sale_order_id")
    def onchange_requote_sale_order_id(self):
        super().onchange_requote_sale_order_id()
        if self.requote_sale_order_id.quote_request_type_id:
            self.show_document_type = self.requote_sale_order_id.show_document_type
            self.show_site_address = self.requote_sale_order_id.show_site_address
            self.show_site_contact = self.requote_sale_order_id.show_site_contact
            self.show_project_detail = self.requote_sale_order_id.show_project_detail
            self.show_job_detail = self.requote_sale_order_id.show_job_detail

    @api.depends("quote_request_type_id")
    def _compute_on_quote_request_type_id(self):
        super()._compute_on_quote_request_type_id()
        for sale in self:
            sale.show_document_type = sale.quote_request_type_id.show_document_type
            sale.show_site_address = sale.quote_request_type_id.show_site_address
            sale.show_site_contact = sale.quote_request_type_id.show_site_contact
            sale.show_project_detail = sale.quote_request_type_id.show_project_detail
            sale.show_job_detail = sale.quote_request_type_id.show_job_detail
            sale.show_document_type = sale.quote_request_type_id.show_document_type

    client_phone = fields.Char(string="Client Phone", size=21)
    client_state_id = fields.Many2one(
        "res.country.state",
        string="State",
        ondelete="restrict",
        default=lambda self: self.env["res.country.state"].search(
            [("name", "=", "Queensland")], limit=1
        ),
    )

    client_state = fields.Char(related="client_state_id.name")
    client_status = fields.Selection(
        [("temporary", "Temporary"), ("permanent", "Permanent")], string="Client Status"
    )
    client_street = fields.Char(string="Street", size=30)
    client_suburb = fields.Char(string="Suburb", size=25)
    client_tag = fields.Char(string="Tag", size=8)
    client_zip = fields.Char(string="Zip Code", change_default=True)

    site_mobile = fields.Char(string="Mobile", size=21)
    site_phone = fields.Char(string="Phone", size=21)
    site_contact = fields.Many2one(
        "res.partner", domain="[('type_contact', '=', 'site_addresses')]"
    )
    supervisor_contact_id = fields.Many2one(
        "res.partner",
        string="Supervisor",
        domain="[('type_contact','=','supervisor'),('builder_id','=', partner_id)]",
    )

    attachment_count = fields.Integer(
        compute="_compute_attachment_count", string="Attachment Count"
    )

    sent_quote_to_customer_stage = fields.Boolean(
        string="Sent to Stairbiz Stage",
    )
    quote_received_from_stairbiz_stage = fields.Boolean(
        string="Quote Received from Stairbiz Stage",
        compute="_compute_sent_quote_to_customer_stage",
    )

    is_sent_quote_to_customer = fields.Boolean(
        string="Sent Quote to Customer", default=False
    )
    stairbiz_quotes_job_ids = fields.One2many(
        "stairbiz_quotes.job",
        "sale_order_id",
        string="Stairbiz Quotes Job IDs",
    )

    @api.depends("stage_id")
    def _compute_sent_quote_to_customer_stage(self):
        sent_quote_to_customer_stage = self.env.ref(
            "stairmaster_sale.sale_order_stage_quotes_sent_to_customer"
        )
        quote_received_from_stairbiz_stage = self.env.ref(
            "stairmaster_crm_stairbiz.sale_order_stage_quote_received_stairbiz"
        )
        for sale in self:
            sale.sent_quote_to_customer_stage = bool(
                sale.stage_id == sent_quote_to_customer_stage
            )
            sale.quote_received_from_stairbiz_stage = bool(
                sale.stage_id == quote_received_from_stairbiz_stage
            )

    @api.depends("team_id", "stairbiz_ids.stairbiz_quote_options_attachment_id")
    def _compute_stage_id(self):
        super()._compute_stage_id()
        quote_received_from_stairbiz_stage = self.env.ref(
            "stairmaster_crm_stairbiz.sale_order_stage_quote_received_stairbiz"
        )
        sent_quote_to_customer_stage = self.env.ref(
            "stairmaster_sale.sale_order_stage_quotes_sent_to_customer"
        )
        sale_order_stage_email_received = self.env.ref(
            "stairmaster_sale.sale_order_stage_email_received"
        )

        for sale in self:
            sale.stage_id = sale_order_stage_email_received.id

            if sale.stairbiz_ids.stairbiz_quote_options_attachment_id:
                if sale.stage_id.sequence < quote_received_from_stairbiz_stage.sequence:
                    sale.stage_id = quote_received_from_stairbiz_stage.id
                if sale.is_sent_quote_to_customer:
                    sale.stage_id = sent_quote_to_customer_stage.id


    def update_site_address_from_site_address(self):
        _logger.info(
            "==== Begin update_site_address_from_site_address with '%s' ====",
            self.site_street,
        )
        try:
            _logger.info("==== Get first Response: %s ====", self.site_street)
            result = self.sh_get_partial_address(self.site_street)

            _logger.info("==== Response: %s ====", result)

            if not result:
                return
            result = result[0]
            _logger.info("==== Get final Response: %s ====", result)
            final_result = self.sh_fill_address(
                result["description"], result["place_id"]
            )
            if final_result:
                site_address = self.with_context(
                    create_new_site_address=True
                ).issue_new_site_address_from_sale_order(final_result)
                if site_address:
                    self.write(
                        {
                            "site_address_id": site_address.id,
                            "quote_request_type_id": self.env.ref(
                                "stairmaster_crm_stairbiz.site_specific_type"
                            ).id,
                        }
                    )
        except Exception as e:
            traceback_str = "".join(traceback.format_tb(e.__traceback__))
            _logger.warning("Error while updating site address: %s", e)
            _logger.warning("Traceback:\n %s", traceback_str)

    # NOTE for API call
    def sh_get_partial_address(self, partial_address):
        company = (
            self.env.user.company_id if self.env.user.company_id else self.env.company
        )
        country_code_list = company.sh_restricted_country_ids.mapped("code")
        if country_code_list:
            result = ["country:" + str(item) + "|" for item in country_code_list]
            test_list = ["".join(result)]

        if (
            company
            and company.sh_is_enable_google_api_key
            and company.sh_google_api_key
        ):
            params = {
                "key": company.sh_google_api_key,
                "fields": "formatted_address,name",
                "inputtype": "textquery",
                "types": "address",
                "input": partial_address,
            }
            if country_code_list and test_list[0]:
                params.update(
                    {
                        "components": test_list[0],
                    }
                )
            try:
                results = requests.get(
                    f"{GOOGLE_MAP_PLACES}/autocomplete/json", params=params, timeout=2.5
                ).json()
            except (TimeoutError, ValueError) as e:
                _logger.warning("Error while fetching partial address: %s", e)
                return []
            results = (
                results.get("predictions", [])
                if results.get("status", False) == "OK"
                else []
            )

            return [
                {"description": result["description"], "place_id": result["place_id"]}
                for result in results
            ]

        return []

    # NOTE for API call
    def sh_fill_address(self, address, place_id):
        company = (
            self.env.user.company_id if self.env.user.company_id else self.env.company
        )
        if (
            address
            and company
            and company.sh_is_enable_google_api_key
            and company.sh_google_api_key
        ):
            params = {
                "key": company.sh_google_api_key,
                "place_id": place_id,
                "fields": "address_component,adr_address",
            }

            try:
                results = requests.get(
                    f"{GOOGLE_MAP_PLACES}/details/json", params=params, timeout=2.5
                ).json()

                html_address = results["result"]["adr_address"]
                results = results["result"]["address_components"]

                for res in results:
                    res["type"] = res.pop("types")[0]

            except (TimeoutError, ValueError):
                return {}

            sequence = list(self.get_google_fields_mapping().keys())
            results.sort(
                key=lambda result: sequence.index(result["type"])
                if result["type"] in sequence
                else 143
            )
            complete_address = self._convert_to_standard_address(results)

            # To avoid missing any type of user-inputted number
            if "number" not in complete_address:
                house_number = (
                    address.replace(complete_address.get("zip", ""), "")
                    .replace(complete_address.get("street", ""), "")
                    .replace(complete_address.get("city", ""), "")
                    .replace("-", "")
                )
                complete_address["number"] = house_number.split(",")[0].strip()
                complete_address[
                    "formatted_street"
                ] = f'{complete_address.get("street", "")} {complete_address["number"]}'
            else:
                html2street = html2plaintext(html_address.split(",")[0])
                street = (
                    f'{complete_address.get("street", "")} {complete_address["number"]}'
                )
                complete_address["formatted_street"] = (
                    html2street if len(html2street) >= len(street) else street
                )
            return complete_address if complete_address else {}
        return {}

    # NOTE for API call
    def sh_directions(self, origin, destination):
        company = (
            self.env.user.company_id if self.env.user.company_id else self.env.company
        )
        if (
            company
            and company.sh_is_enable_google_api_key
            and company.sh_google_api_key
        ):
            params = {
                "key": company.sh_google_api_key,
                "origin": origin,
                "destination": destination,
            }
            try:
                results = requests.get(
                    f"{GOOGLE_DIRECTIONS}/json", params=params, timeout=2.5
                ).json()
            except (TimeoutError, ValueError):
                return {}
            return results
        return {}

    def _convert_to_standard_address(self, g_fields):
        """METHOD OF SOFTHEALER TECHNOLOGIES.

        This function converts Google Maps API address fields to standard address fields in Odoo.

        :param g_fields: It is a list of dictionaries containing information about the address fields
        returned by the Google Maps API. Each dictionary represents a single address component, such as
        street number, city, state, etc. The dictionary contains keys such as 'long_name', 'short_name',
        and 'type', which provide information about
        :return: a dictionary containing the standard address values extracted from the input Google
        fields.
        """

        address_vals = {}
        google_fields_mapping = self.get_google_fields_mapping()
        for g_field in g_fields:
            fields_standard = (
                google_fields_mapping[g_field["type"]]
                if g_field["type"] in google_fields_mapping
                else []
            )

            for s_field in fields_standard:
                if s_field in address_vals:
                    continue
                if s_field == "country":
                    country = self.env["res.country"].search(
                        [("code", "=", g_field["short_name"].upper())], limit=1
                    )
                    address_vals[s_field] = country.id if country else False
                    address_vals["country_code"] = country.code if country else False
                elif s_field == "state":
                    domain = [("code", "=", g_field["short_name"].upper())]
                    if address_vals["country"]:
                        domain = expression.AND(
                            [domain, [("country_id.id", "=", address_vals["country"])]]
                        )
                    state = self.env["res.country.state"].search(domain)
                    if len(state) == 1:
                        address_vals[s_field] = state.id
                else:
                    address_vals[s_field] = g_field["long_name"]
        return address_vals

    def get_google_fields_mapping(self):
        return {
            "country": ["country"],
            "street_number": ["number"],
            "administrative_area_level_3": ["city"],
            "neighborhood": [""],
            "locality": ["city"],
            "route": ["street"],
            "sublocality_level_1": ["street2"],
            "postal_code": ["zip"],
            "administrative_area_level_1": ["state", "city"],
            "administrative_area_level_2": ["state", "country"],
        }

    def model_process_document(self):
        if not self.site_address_id:
            self.update_site_address_from_site_address()

    def action_sent_quote_to_customer(self):
        self.ensure_one()
        ctx = dict(self.env.context or {})
        template = self.env.ref(
            "stairmaster_crm_stairbiz.email_template_sent_quote_to_customer"
        )
        ctx.update(
            {
                "dialog_size": "large",
                "default_model": "sale.order",
                "default_res_ids": self.ids,
                "default_template_id": template.id,
                "default_attachment_ids": [
                    (
                        6,
                        0,
                        self.stairbiz_ids.stairbiz_quote_options_attachment_id.ids
                        or [],
                    )
                ],
            }
        )
        self.is_sent_quote_to_customer = True
        return {
            "name": "Compose Email sent to Customer",
            "type": "ir.actions.act_window",
            "view_mode": "form",
            "res_model": "mail.compose.message",
            "target": "new",
            "context": ctx,
        }

    is_stairbiz_quote_team = fields.Boolean(
        compute="_compute_is_stairbiz_quote_team",
    )

    def _compute_is_stairbiz_quote_team(self):
        for order in self:
            order.is_stairbiz_quote_team = order.team_id == self.env.ref(
                "stairmaster_crm_stairbiz.sale_order_team_stairbiz_quotes"
            )

    @api.onchange("is_stairbiz")
    def onchange_is_stairbiz(self):
        if self.is_stairbiz == "yes":
            self.is_site_specific = True

    is_site_specific = fields.Boolean(
        string="Is this a site specific quote?", default=False
    )
    is_house_design = fields.Boolean(string="Is this a Standards quote?", default=False)

    def _get_selection_label(self, field_name, value):
        return dict(self._fields[field_name].selection).get(value, "N/A")

    def generate_stairbiz_job(self):
        self.ensure_one()
        if not self.stairbiz_id:
            # Create the StairBiz record if it doesn't exist
            stairbiz = self.env["stairbiz.stairbiz"].create(
                {
                    "order_id": self.id,
                    "name": self.name,
                    "site_address_id": self.site_address_id.id,
                    "quote_number": self.name,
                    "quote_date": self.date_order,
                    "quote_price": self.amount_total,
                    "quote_representative": self.user_id.name,
                }
            )
            self.stairbiz_id = stairbiz.id

        return self.stairbiz_id.generate_stairbiz_job()

    def regenerate_stairbiz_job(self):
        self.ensure_one()
        if not self.stairbiz_id:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": "Warning",
                    "message": "No StairBiz record exists for this sale order.",
                    "type": "warning",
                },
            }
        return self.stairbiz_id.regenerate_stairbiz_job()

    def preview_stairbiz_job(self):
        self.ensure_one()
        if not self.stairbiz_id:
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": "Warning",
                    "message": "No StairBiz record exists for this sale order.",
                    "type": "warning",
                },
            }
        return self.stairbiz_id.preview_stairbiz_job()

    sh_contact_google_location = fields.Char("Enter Location")

    sh_contact_place_text = fields.Char("Enter location", copy=False)
    sh_contact_place_text_main_string = fields.Char("Enter location ", copy=False)

    @api.onchange("sh_contact_place_text_main_string")
    def onchange_technical_google_text_main_string(self):
        """to save name in google field"""
        if self.sh_contact_place_text_main_string:
            self.sh_contact_google_location = ""

    @api.depends("sh_contact_place_text", "site_street", "partner_child_ids")
    def _compute_site_address_id(self):
        for record in self:
            if record.sh_contact_place_text:
                # CHECK IF PARTNER EXISTS WITH NEW ADDRESS ASSIGN TO SITE_ADDRESS
                # IF NOT CREATE NEW
                google_place_dict = json.loads(record.sh_contact_place_text)
                if google_place_dict:
                    record = record.with_context(create_new_site_address=True)
                    site_address = record.issue_new_site_address_from_sale_order(
                        {
                            "street": google_place_dict.get("formatted_street", "")
                            or f'{google_place_dict.get("number","")} {google_place_dict.get("street","")}',
                            "city": google_place_dict.get("city", ""),
                            "zip": google_place_dict.get("zip", ""),
                            "state_id": google_place_dict.get("state", False),
                        }
                    )
                    record.site_address_id = site_address.id

    def _inverse_site_address_id(self):
        for record in self:
            if record.site_address_id:
                record.site_street = record.site_address_id.street
                record.site_suburb = record.site_address_id.city
                record.site_zip = record.site_address_id.zip
                record.site_state = record.site_address_id.state_id.name

    @api.onchange("sh_contact_place_text")
    def onchange_technical_google_text(self):
        """to place info to std. address fields"""
        if self.sh_contact_place_text:
            google_place_dict = json.loads(self.sh_contact_place_text)
            # CHECK IF PARTNER EXISTS WITH NEW ADDRESS ASSIGN TO SITE_ADDRESS
            # IF NOT CREATE NEW
            partner = self.env["res.partner"].search(
                [
                    ("street", "=", google_place_dict.get("formatted_street", "")),
                    ("city", "=", google_place_dict.get("city", "")),
                ],
                limit=1,
            )
            if partner:
                self.site_address_id = partner.id
            if google_place_dict:
                self.write(
                    {
                        "site_street": google_place_dict.get("formatted_street", "")
                        or f'{google_place_dict.get("number","")} {google_place_dict.get("street","")}',
                        "site_suburb": google_place_dict.get("city", ""),
                        "site_zip": google_place_dict.get("zip", ""),
                        "site_state": google_place_dict.get("state", False),
                    }
                )

    site_address_id = fields.Many2one(
        "res.partner",
        string="Site Address",
        compute="_compute_site_address_id",
        inverse="_inverse_site_address_id",
        readonly=False,
        store=True,
    )
    partner_child_ids = fields.Many2many(
        "res.partner",
        string="Partner Child Addresses",
        compute="_compute_partner_child_ids",
    )
    flag = fields.Boolean(string="Flag")

    @api.depends("partner_id")
    def _compute_partner_child_ids(self):
        for record in self:
            if record.partner_id and record.partner_id.child_ids:
                record.partner_child_ids = record.partner_id.child_ids.filtered(
                    lambda child: child.type_contact == "site_addresses"
                ).ids
            else:
                record.partner_child_ids = [(6, 0, [])]

    @api.onchange("site_address_id")
    def onchange_site_address_id(self):
        if self.site_address_id:
            self.site_street = self.site_address_id.street
            self.site_suburb = self.site_address_id.city
            self.site_zip = self.site_address_id.zip
            self.site_state = self.site_address_id.state_id.name
            if not self.quote_request_type_id:
                self.quote_request_type_id = self.env.ref(
                    "stairmaster_crm_stairbiz.site_specific_type"
                ).id
        if self.is_site_specific:
            self.project_name = self.site_address_id.name
        if self.site_address_id and self.partner_id:
            address_ids = self.partner_id.child_ids
            if self.site_address_id.id not in address_ids.ids:
                self.site_address_id.type_contact = "site_addresses"
                self.site_address_id.type = "site_addresses"
                self.partner_id.write({"child_ids": [(4, self.site_address_id.id)]})

            duplicate_pipelines = self.env["sale.order"].search(
                [
                    ("site_address_id", "=", self.site_address_id.id),
                    ("partner_id", "=", self.partner_id.id),
                    ("id", "!=", self._origin.id),
                ]
            )
            if duplicate_pipelines:
                self.flag = True
            else:
                self.flag = False
        else:
            self.flag = False

    @api.onchange("site_street", "site_suburb", "site_zip", "site_state")
    def onchange_site_address(self):
        full_address = (
            self.site_street and self.site_suburb and self.site_zip and self.site_state
        )
        site_state_record = self.env["res.country.state"].search(
            [("name", "=", self.site_state)], limit=1
        )
        if full_address and self.site_address_id:
            result = self.env["res.partner"]._geo_localize(
                self.site_street,
                self.site_zip,
                self.site_suburb,
                self.site_state,
                self.site_address_id.country_id.name,
            )
            if result:

                self.site_address_id.write(
                    {
                        "street": self.site_street,
                        "city": self.site_suburb,
                        "zip": self.site_zip,
                        "state_id": site_state_record.id,
                        "country_id": self.site_address_id.country_id.id,
                        "partner_latitude": result[0],
                        "partner_longitude": result[1],
                        "date_localization": fields.Date.context_today(self),
                    }
                )
        elif full_address:
            partner = self.issue_new_site_address_from_sale_order(
                {
                    "street": self.site_street,
                    "city": self.site_suburb,
                    "zip": self.site_zip,
                    "state_id": site_state_record.id,
                }
            )
            self.site_address_id = partner.id
        self.write(
            {
                "site_street": self.site_street,
                "site_suburb": self.site_suburb,
                "site_zip": self.site_zip,
                "site_state": site_state_record.name,
                "site_city": self.site_address_id.city,
                "site_phone": self.site_address_id.phone,
                "site_mobile": self.site_address_id.mobile,
            }
        )

    @api.model_create_multi
    def create(self, vals):
        res = super(SaleOrder, self).create(vals)
        return res

    def write(self, vals):
        res = super(SaleOrder, self).write(vals)
        for record in self:
            if vals.get("sh_contact_place_text") or self.env.context.get(
                "issue_new_site_address"
            ):
                if record.site_address_id:
                    continue
                site_addresses = record.issue_new_site_address_from_sale_order(
                    {
                        "street": record.site_street,
                        "city": record.site_suburb,
                        "zip": record.site_zip,
                        "state": record.site_state,
                    }
                )
                record.site_address_id = site_addresses.id
            if vals.get("stairbiz_ids"):
                record._update_stairbiz_quotes_records(vals)
        return res

    def _update_stairbiz_quotes_records(self, vals):
        _logger.info("==== Update stairbiz quotes records ====")
        add_stairbiz_ids = [item[1] for item in vals.get("stairbiz_ids") if item[0] == 4]
        remove_stairbiz_ids = [item[1] for item in vals.get("stairbiz_ids") if item[0] == 3]
        add_stairbiz = self.env["stairbiz.stairbiz"].browse(add_stairbiz_ids)   
        remove_stairbiz = self.env["stairbiz.stairbiz"].browse(remove_stairbiz_ids)
        # Update order_id in stairbiz.stairbiz records
        for stairbiz in add_stairbiz:
            if not stairbiz.order_id or stairbiz.order_id.id != self.id:
                stairbiz.write({
                    'order_id': self.id,
                    'odoo_stairbiz_status': 'allocated',
                    'allocation_date': fields.Date.context_today(self),
                })
                _logger.info("Allocated stairbiz for StairBiz quote %s", stairbiz.name)

        for stairbiz in remove_stairbiz:
            if stairbiz.order_id and stairbiz.order_id.id == self.id:
                stairbiz.write({
                    'order_id': False,
                    'odoo_stairbiz_status': 'available',
                    'allocation_date': False,
                })
                _logger.info("Unallocated stairbiz for StairBiz quote %s", stairbiz.name)

    def issue_new_site_address_from_sale_order(self, info_dict={}):
        _logger.info(
            "==== Begin issue_new_site_address_from_sale_order with '%s' ====",
            info_dict,
        )
        required_domain = [("type_contact", "=", "site_addresses")]
        if info_dict:
            if info_dict.get("street", ""):
                street_condition = [("street", "ilike", info_dict.get("street", ""))]
                domain = expression.OR(
                    [expression.AND([required_domain, street_condition])]
                )
            # city_condition = [("city", "ilike", info_dict.get("city", ""))]
            # zip_condition = [("zip", "ilike", info_dict.get("zip", ""))]
            # state_condition = [("state_id", "=", info_dict.get("state_id", False))]
            # country_condition = [("country_id", "=", info_dict.get("country_id", False))]
            if info_dict.get("formatted_street", ""):
                formatted_street_condition = [
                    ("street", "ilike", info_dict.get("formatted_street", ""))
                ]
                domain = expression.OR(
                    [
                        expression.AND([required_domain, formatted_street_condition]),
                    ]
                )

            partner = self.env["res.partner"].search(domain, limit=1)
            if partner:
                _logger.info("==== Find suitable site address'%s' ====", partner.name)
            elif self.env.context.get("create_new_site_address"):
                if info_dict.get("state_id") or info_dict.get("state"):
                    state = self.env["res.country.state"].browse(
                        info_dict.get("state_id", info_dict.get("state"))
                    )
                else:
                    state_name = info_dict.get("state")
                    state = self.env["res.country.state"].search(
                        [("name", "=", state_name)], limit=1
                    )
                name = self.env["ir.sequence"].next_by_code("partner.site.address.new")
                if state.name == "Queensland":
                    name = name[:3] + "Q-" + name[3:]
                elif state.name == "New South Wales":
                    name = name[:3] + "N-" + name[3:]

                partner_vals = {
                    "name": name,
                    "type": "site_addresses",
                    "street": info_dict.get("formatted_street")
                    or info_dict.get("street", self.site_street or ""),
                    "city": info_dict.get("city", self.site_suburb or ""),
                    "zip": info_dict.get("zip", self.site_zip or ""),
                    "state_id": state.id,
                    "type_contact": "site_addresses",
                    "country_id": info_dict.get("country_id"),
                    "parent_id": self.partner_id.id,
                    "auto_issue": True,
                }
                # Update geo localization
                geo_results = self.env["res.partner"]._geo_localize(
                    info_dict.get("formatted_street", self.site_street or ""),
                    info_dict.get("zip", self.site_zip or ""),
                    info_dict.get("city", self.site_suburb or ""),
                    state.name,
                    info_dict.get("country_id", self.site_country or ""),
                )
                if geo_results:
                    partner_vals.update(
                        {
                            "partner_latitude": geo_results[0],
                            "partner_longitude": geo_results[1],
                            "date_localization": fields.Date.context_today(self),
                        }
                    )

                self.env["mail.message"].create(
                    {
                        "body": f"New site address created: {partner_vals['name']}",
                        "subject": "Site Address Creation",
                        "message_type": "notification",
                        "subtype_id": self.env.ref("mail.mt_note").id,
                        "model": self._name,
                        "res_id": self.id,
                    }
                )
                _logger.info(
                    "==== Create new site address with '%s' ====", partner_vals
                )

                partner = self.env["res.partner"].create(partner_vals)
        return partner

    def _create_quotation_from_order(self, vals_quotation={}, groq_doc_list=[]):
        quotation_context = self._prepare_opportunity_quotation_context()
        if quotation_context:
            vals_quotation.update(
                {
                    "team_id": quotation_context.get("default_team_id"),
                    "campaign_id": quotation_context.get("default_campaign_id"),
                    "partner_id": quotation_context.get("default_partner_id"),
                    "opportunity_id": quotation_context.get("default_opportunity_id"),
                    "medium_id": quotation_context.get("default_medium_id"),
                    "source_id": quotation_context.get("default_source_id"),
                    "company_id": quotation_context.get("default_company_id")
                    or self.env.company.id,
                }
            )
            if self.team_id:
                vals_quotation.update(
                    {
                        "team_id": self.team_id.id,
                    }
                )
            if self.user_id:
                vals_quotation.update(
                    {
                        "user_id": self.user_id.id,
                    }
                )
        sale_order = self.env["sale.order"].create(vals_quotation)
        return sale_order

    def update_groq_doc_with_sale_order(self, groq_doc_list, sale_order):
        if groq_doc_list and sale_order:
            groq_docs = self.env["groq.document"].browse(groq_doc_list)
            groq_docs.write({"order_id": sale_order.id})
        return

    # For api call
    @api.model
    def handle_stairbiz_quotes_from_email_sent(self, email_content):
        email_content = refine_email_content(email_content)
        _logger.info(
            "==== Begin handle_stairbiz_quotes_from_email_sent with '%s' ====",
            email_content,
        )
        order_vals = self._update_vals_stairbiz_quote_creation(email_content)
        sale_order = self.create(order_vals)
        sale_order._post_stairbiz_quote_email_content_to_saleorder(email_content)

        if sale_order.message_ids:
            sale_order.update_sale_order_from_order_quotes_email()
        return True

    def _update_vals_stairbiz_quote_creation(self, email_content):
        order_vals = {
            "name": email_content.get("subject"),
            "team_id": self.env.ref(
                "stairmaster_crm_stairbiz.sale_order_team_stairbiz_quotes"
            ).id,
            "stage_id": self.env.ref(
                "stairmaster_sale.sale_order_stage_quotes_sent_to_customer"
            ).id,
        }
        if email_content.get("to"):
            order_vals.update(
                self.update_partner_from_email({"email_from": email_content.get("to")})
            )
        return order_vals

    def update_sale_order_from_order_quotes_email(self):
        if self.team_id == self.env.ref(
            "stairmaster_crm_stairbiz.sale_order_team_stairbiz_quotes"
        ):
            res = self.update_stairbiz_quotes_from_email()
            if not self.site_address_id:
                self.update_site_address_from_site_address()
            return res
        else:
            return super().update_sale_order_from_order_quotes_email()

    def update_stairbiz_quotes_from_email(self):
        mail_ids = self.message_ids.filtered(lambda a: a.message_type == "email")
        partner = self.retrive_main_partner(mail_ids)
        if partner:
            self.partner_id = partner
        for mail in mail_ids:
            try:
                self._update_sale_order_from_email(mail)
            except Exception as e:
                traceback_str = "".join(traceback.format_tb(e.__traceback__))
                raise ValidationError(
                    f"Error while processing email: {e} \n {traceback_str}"
                )

    def _post_stairbiz_quote_email_content_to_saleorder(self, email_content):
        # Create attachments for email
        vals_attachment = []
        attachments = self.env["ir.attachment"]
        if email_content.get("attachments"):
            for attachment in email_content["attachments"]:
                content = attachment["base64"]
                if attachment["file_name"].lower().endswith(".pdf"):
                    vals_attachment.append(
                        {
                            "name": attachment["file_name"],
                            "datas": content.encode(),
                            "mimetype": "application/pdf",
                            "type": "binary",
                            "res_model": "sale.order",
                            "public": True,
                        }
                    )
            if vals_attachment:
                attachments = self.env["ir.attachment"].create(vals_attachment)
        vals_message = {
            "body": email_content.get("body"),
            "subject": email_content.get("subject"),
            "message_type": "email",
            "date": email_content.get("date"),
            "message_id": email_content.get("msg_id"),
            "subtype_id": self.env.ref("mail.mt_comment").id,
            "attachment_ids": attachments.ids if attachments else [],
        }
        self.message_post(**vals_message)
        return True

    def update_groq_type_from_attachment(self, attachment):
        groq_doc = super().update_groq_type_from_attachment(attachment)
        if self.team_id == self.env.ref(
            "stairmaster_crm_stairbiz.sale_order_team_stairbiz_quotes"
        ):
            groq_types = self.env["groq.document.type"].search_fetch(
                [
                    ("partner_id", "=", self.partner_id.id),
                    ("name", "ilike", "Stairbiz Quote"),
                ],
                ["id", "name"],
            )
            if groq_types:
                groq_doc.groq_document_type_ids = [
                    (4, groq_type_id) for groq_type_id in groq_types.ids
                ]
        return groq_doc

    def _custom_fetch_related_record(
        self, document_type, field_model, field_value, field_name
    ):
        record = super()._custom_fetch_related_record(
            document_type, field_model, field_value, field_name
        )
        if field_model == "res.partner" and field_name == "site_address_id":
            record = self._get_site_address_record(field_value)
        return record

    def _get_site_address_record(self, field_value):
        field_value_list = field_value.split(" ")
        related_record = self.env["res.partner"]
        for i in range(0, len(field_value_list)):
            if i + 2 < len(field_value_list):
                related_record = self.env["res.partner"].search(
                    [
                        ("street", "ilike", field_value_list[i]),
                        ("street", "ilike", field_value_list[i + 1]),
                        ("street", "ilike", field_value_list[i + 2]),
                    ],
                    limit=1,
                )
                if related_record:
                    return related_record
        if not related_record:
            related_record = self.with_context(
                create_new_site_address=True
            ).issue_new_site_address_from_sale_order(
                {
                    "street": field_value,
                }
            )
        return related_record

    # for Airflow process_google_drive_pdfs_with_upload call
    @api.model
    def create_sale_order_from_stairbiz_pdf_content(self, file_name):

        # Create sale order with TBA partner
        partner_tba = self.env.ref("stairmaster_contact_extened.tba_partner")
        order = self.create(
            {
                "name": file_name.replace(".pdf", ""),
                "partner_id": partner_tba.id,
                "team_id": self.env.ref(
                    "stairmaster_crm_stairbiz.sale_order_team_stairbiz_quotes"
                ).id,
                "stage_id": self.env.ref(
                    "stairmaster_crm_stairbiz.sale_order_stage_quote_received_stairbiz"
                ).id,
                "quote_request_type_id": self.env.ref(
                    "stairmaster_crm_stairbiz.site_specific_type"
                ).id,
            }
        )

        return order.ids

    # for Airflow process_google_drive_pdfs_with_upload call
    @api.model
    def create_groq_document_from_stairbiz_pdf_content(
        self, order_id=None, pdf_content_list=None
    ):
        _logger.info(
            f"Creating groq document from stairbiz pdf content for order {order_id}"
        )
        # Create attachment for the PDF content
        if not order_id:
            _logger.error("Order ID is required to create groq document")
            return False
        order = self.browse(order_id)

        if not pdf_content_list:
            _logger.error("PDF content list is required to create groq document")
            return False

        pdf_content = (
            pdf_content_list[0] if len(pdf_content_list) == 1 else pdf_content_list
        )

        attachment = self.env["ir.attachment"].create(
            {
                "name": "Stairbiz Quote",
                "datas": pdf_content.encode()
                if isinstance(pdf_content, str)
                else pdf_content,
                "mimetype": "application/pdf",
                "type": "binary",
                "res_model": "sale.order",
                "res_id": order.id,
                "sale_order_id": order.id,
                "res_field": "stairbiz_quote_options_attachment_ids",
                "public": True,
                "is_stairbiz_document": True,
            }
        )

        # Create and process groq document
        document_type = self.env.ref(
            "stairmaster_crm_stairbiz.global_stairbiz_quote_type"
        )
        groq_document = self.env["groq.document"].create(
            {
                "res_model": "sale.order",
                "res_id": order.id,
                "is_stairbiz_document": True,
                "attachment_id": attachment.id,
                "groq_document_type_ids": [(4, document_type.id)],
            }
        )

        groq_document.process_document()

        # Update attachment name if quote number exists
        if order.quote_number:
            attachment.name = attachment.update_order_by_quote_number(
                order.quote_number
            )
        _logger.info(
            f"Created groq document from stairbiz pdf content for order {order_id}"
        )
        return True

    def cron_match_stairbiz_job_to_sale_order(self):
        _logger.info("=========Starting cron_match_stairbiz_job_to_sale_order=========")
        time_48_hours_ago = datetime.now() - timedelta(hours=48)

        # Search for stairbiz_quotes.job records created in the last 48 hours and not yet synced
        stairbiz_jobs = self.env["stairbiz_quotes.job"].search_fetch(
            [
                ("create_date", ">=", time_48_hours_ago),
                ("is_sync_odoo_quote", "=", False),
            ],
            ["id", "odoo_site_address_id", "is_sync_odoo_quote"],
        )

        for job in stairbiz_jobs:
            # Find matching sale.order based on odoo_site_address_id and site_address_id
            if job.odoo_site_address_id:
                sale_order = self.env["sale.order"].search_fetch(
                    [
                        ("site_address_id", "=", job.odoo_site_address_id),
                        ["id", "stairbiz_quotes_job_ids", "site_address_id"],
                    ],
                    limit=1,
                )

            if sale_order:
                # Add the job to the stairbiz_quotes_job_ids field of the sale.order
                sale_order.write({"stairbiz_quotes_job_ids": [(4, job.id)]})
                # Set is_sync_odoo_quote to True for the job
                job.write({"is_sync_odoo_quote": True, "sale_order_id": sale_order.id})
                update_job_info = job.get_job_infor()
                sale_order.write(update_job_info)
        _logger.info("=========Ending cron_match_stairbiz_job_to_sale_order=========")
        return True

    def action_view_stairbiz_quotes(self):
        self.ensure_one()
        if not self.stairbiz_id:
            return
        return {
            "type": "ir.actions.act_window",
            "res_model": "stairbiz.stairbiz",
            "views": [(False, "form")],
            "res_id": self.stairbiz_id.id,
            "context": {"create": False},
        }
