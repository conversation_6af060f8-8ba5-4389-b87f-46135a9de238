[options]
admin_passwd = 1
http_port = 8069
## for gevent
# http_port = 8072
logfile = /var/log/odoo/odoo-server.log
addons_path = /home/<USER>/odoo/itms/stairmaster18/odoo/addons, /home/<USER>/odoo/itms/stairmaster18/sign, /home/<USER>/odoo/itms/stairmaster18/itms_automation, /home/<USER>/odoo/itms/stairmaster18/odoo-marco/design-themes, /home/<USER>/odoo/itms/stairmaster18/edi, /home/<USER>/odoo/itms/stairmaster18/web, /home/<USER>/odoo/itms/stairmaster18/edi-framework, /home/<USER>/odoo/itms/stairmaster18/connector, /home/<USER>/odoo/itms/stairmaster18/bank-payment, /home/<USER>/odoo/itms/stairmaster18/odoo_itms_business, /home/<USER>/odoo/itms/stairmaster18/odoo_itms_apps, /home/<USER>/odoo/itms/stairmaster18/server-brand, /home/<USER>/odoo/itms/stairmaster18/timesheet, /home/<USER>/odoo/itms/stairmaster18/stairmaster, /home/<USER>/odoo/itms/stairmaster18/queue,

# /home/<USER>/odoo/itms/stairmaster18/stairmaster
proxy_mode = true
server_wide_modules = base,muk_rest
# workers = 4
# xmlrpc_port = 8069
limit_time_real=1200000
limit_memory_real = **********
limit_memory_hard = **********
# log_level = debug
update = true