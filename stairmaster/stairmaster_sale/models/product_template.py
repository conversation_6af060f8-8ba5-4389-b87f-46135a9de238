# -*- coding: utf-8 -*-
from odoo import api, fields, models


class ProductTemplate(models.Model):
    _inherit = "product.template"

    customer_specific_id = fields.Many2one("res.partner", string="Customer Specific")
    create_from_groq = fields.Boolean(string="Create from Groq", default=False)


class ProductProduct(models.Model):
    _inherit = "product.product"

    def create_record_from_groq(self, vals_list):
        for vals in vals_list:
            vals['create_from_groq'] = True
            vals['name'] = vals
        return super().create_record_from_groq(vals_list)
