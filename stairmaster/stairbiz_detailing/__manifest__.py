{
    "name": "StairBiz detailing",
    "version": "********.0",
    "summary": "Integration with StairBiz Server",
    "description": "This module handles detailing data for StairBiz.",
    "author": "Markshaw",
    "category": "Custom",
    "depends": ["base"],
    "data": [
        "security/ir.model.access.csv",
        "views/stairbiz_detailing_client_views.xml",
        "views/stairbiz_detailing_sysdiagram_views.xml",
        "views/stairbiz_detailing_actgroup_views.xml",
        "views/stairbiz_detailing_activity_views.xml",
        "views/stairbiz_detailing_clientcontact_views.xml",
        "views/stairbiz_detailing_detail_views.xml",
        "views/stairbiz_detailing_done_views.xml",
        "views/stairbiz_detailing_inventory_views.xml",
        "views/stairbiz_detailing_jobfolder_views.xml",
        "views/stairbiz_detailing_joblabor_views.xml",
        "views/stairbiz_detailing_job_views.xml",
        "views/stairbiz_detailing_mydatafield_views.xml",
        "views/stairbiz_detailing_mydatafieldvalue_views.xml",
        "views/stairbiz_detailing_note_views.xml",
        "views/stairbiz_detailing_payment_views.xml",
        "views/stairbiz_detailing_project_views.xml",
        "views/stairbiz_detailing_quotation_views.xml",
        "views/stairbiz_detailing_sharedconfig_views.xml",
        "views/stairbiz_detailing_sharedconfigctl_views.xml",
        "views/stairbiz_detailing_site_views.xml",
        "views/stairbiz_detailing_snua_views.xml",
        "views/stairbiz_detailing_user_views.xml",
        "views/stairbiz_detailing_version_views.xml",
        "views/stairbiz_detailing_blobunique_views.xml",
        "views/stairbiz_detailing_blobsshared_views.xml",
        "views/stairbiz_detailing_menu_views.xml",

    ],
    "installable": True,
    "application": True,
    "license": "LGPL-3",
}
