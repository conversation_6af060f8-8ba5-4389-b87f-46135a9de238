from odoo import models, fields, api

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'
    
    # Batch configuration fields moved from stairbiz_integration_config
    batch_name = fields.Char(string="Batch Name", config_parameter='stairbiz_quotes.batch_name')
    batch_type = fields.Selection([
        ('quote', 'Quote'),
        ('job', 'Job'),
        ('design', 'Design'),
    ], string="Batch Type", config_parameter='stairbiz_quotes.batch_type', default='quote')
    entries_to_generate = fields.Integer(string="Number of Entries to Generate", 
                                        config_parameter='stairbiz_quotes.entries_to_generate', default=1)
    
    # Reference configuration
    quote_prefix = fields.Char(string="Quote Reference Prefix", 
                              config_parameter='stairbiz_quotes.quote_prefix', default="Q")
    job_prefix = fields.Char(string="Job Reference Prefix", 
                            config_parameter='stairbiz_quotes.job_prefix', default="J")
    design_prefix = fields.Char(string="Design Reference Prefix", 
                               config_parameter='stairbiz_quotes.design_prefix', default="D")
    
    # Current numbers (automatically incremented)
    current_quote_number = fields.Integer(string="Current Quote Number", 
                                         config_parameter='stairbiz_quotes.current_quote_number', default=1000)
    current_job_number = fields.Integer(string="Current Job Number", 
                                       config_parameter='stairbiz_quotes.current_job_number', default=1000)
    current_design_number = fields.Integer(string="Current Design Number", 
                                          config_parameter='stairbiz_quotes.current_design_number', default=1000) 