=================
Components Events
=================

.. 
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! This file is generated by oca-gen-addon-readme !!
   !! changes will be overwritten.                   !!
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
   !! source digest: sha256:97bef8a6c3971c475f2ebe58ebed781490658d2174669d49ee22cea9869fc0a0
   !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

.. |badge1| image:: https://img.shields.io/badge/maturity-Beta-yellow.png
    :target: https://odoo-community.org/page/development-status
    :alt: Beta
.. |badge2| image:: https://img.shields.io/badge/licence-LGPL--3-blue.png
    :target: http://www.gnu.org/licenses/lgpl-3.0-standalone.html
    :alt: License: LGPL-3
.. |badge3| image:: https://img.shields.io/badge/github-OCA%2Fconnector-lightgray.png?logo=github
    :target: https://github.com/OCA/connector/tree/18.0/component_event
    :alt: OCA/connector
.. |badge4| image:: https://img.shields.io/badge/weblate-Translate%20me-F47D42.png
    :target: https://translation.odoo-community.org/projects/connector-18-0/connector-18-0-component_event
    :alt: Translate me on Weblate
.. |badge5| image:: https://img.shields.io/badge/runboat-Try%20me-875A7B.png
    :target: https://runboat.odoo-community.org/builds?repo=OCA/connector&target_branch=18.0
    :alt: Try me on Runboat

|badge1| |badge2| |badge3| |badge4| |badge5|

This module implements an event system (`Observer
pattern <https://en.wikipedia.org/wiki/Observer_pattern>`__) and is a
base block for the Connector Framework. It can be used without using the
full Connector though. It is built upon the ``component`` module.

Documentation: http://odoo-connector.com/

**Table of contents**

.. contents::
   :local:

Usage
=====

As a developer, you have access to a events system. You can find the
documentation in the code or on http://odoo-connector.com

In a nutshell, you can create trigger events:

::

   class Base(models.AbstractModel):
       _inherit = 'base'

       @api.model
       def create(self, vals):
           record = super(Base, self).create(vals)
           self._event('on_record_create').notify(record, fields=vals.keys())
           return record

And subscribe listeners to the events:

::

   from odoo.addons.component.core import Component
   from odoo.addons.component_event import skip_if

   class MagentoListener(Component):
       _name = 'magento.event.listener'
       _inherit = 'base.connector.listener'

       @skip_if(lambda self, record, **kwargs: self.no_connector_export(record))
       def on_record_create(self, record, fields=None):
           """ Called when a record is created """
           record.with_delay().export_record(fields=fields)

This module triggers 3 events:

-  ``on_record_create(record, fields=None)``
-  ``on_record_write(record, fields=None)``
-  ``on_record_unlink(record)``

Changelog
=========

Next
----

********.0 (2018-11-26)
-----------------------

-  [MIGRATION] from 12.0 branched at rev. 324e006

Bug Tracker
===========

Bugs are tracked on `GitHub Issues <https://github.com/OCA/connector/issues>`_.
In case of trouble, please check there if your issue has already been reported.
If you spotted it first, help us to smash it by providing a detailed and welcomed
`feedback <https://github.com/OCA/connector/issues/new?body=module:%20component_event%0Aversion:%2018.0%0A%0A**Steps%20to%20reproduce**%0A-%20...%0A%0A**Current%20behavior**%0A%0A**Expected%20behavior**>`_.

Do not contact contributors directly about support or help with technical issues.

Credits
=======

Authors
-------

* Camptocamp

Contributors
------------

-  Guewen Baconnier <<EMAIL>>

Other credits
-------------

The migration of this module from 17.0 to 18.0 was financially supported
by Camptocamp.

Maintainers
-----------

This module is maintained by the OCA.

.. image:: https://odoo-community.org/logo.png
   :alt: Odoo Community Association
   :target: https://odoo-community.org

OCA, or the Odoo Community Association, is a nonprofit organization whose
mission is to support the collaborative development of Odoo features and
promote its widespread use.

This module is part of the `OCA/connector <https://github.com/OCA/connector/tree/18.0/component_event>`_ project on GitHub.

You are welcome to contribute. To learn how please visit https://odoo-community.org/page/Contribute.
