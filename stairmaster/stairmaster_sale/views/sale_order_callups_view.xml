<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Callups Form View -->
        <record id="view_sale_order_callups_form" model="ir.ui.view">
            <field name="name">sale.order.callups.form</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="stairmaster_sale.view_order_form_inherit"/>
            <field name="mode">primary</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <!-- Add custom class to form -->
                <form position="attributes">
                    <attribute name="class" add="callups_form_view" separator=" "/>
                </form>
                
                <!-- Simplify Sales Type dropdown for Callups using radio buttons -->
                <field name="sale_type" position="attributes">
                    <attribute name="widget">radio</attribute>
                    <attribute name="options">{'horizontal': true}</attribute>
                </field>
                
                <!-- Change Designer to use selection widget -->
                <field name="designer_id" position="attributes">
                    <attribute name="widget">selection</attribute>
                </field>
                
                <!-- Hide certain pages that aren't needed for Callups -->
                <page name="document_clasification" position="attributes">
                    <attribute name="invisible">1</attribute>
                </page>
                
                <!-- Add a dedicated Callups information page -->
                <notebook position="inside">
                    <page name="callups_info" string="Callup Information">
                        <group>
                            <group>
                                <field name="job_number" required="1"/>
                                <field name="team_id" invisible="0" options="{'no_create': true}"/>
                            </group>
                            <group>
                                <field name="is_site_specific" widget="boolean_toggle"/>
                            </group>
                        </group>
                    </page>
                </notebook>
            </field>
        </record>
        
        <!-- Callups Tree/List View -->
        <record id="view_sale_order_callups_tree" model="ir.ui.view">
            <field name="name">sale.order.callups.tree</field>
            <field name="model">sale.order</field>
            <field name="arch" type="xml">
                <list string="Callups">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="job_number"/>
                    <field name="designer_id"/>
                    <field name="stage_id"/>
                    <field name="create_date" optional="show"/>
                </list>
            </field>
        </record>
        
        <!-- Callups Action -->
        <record id="action_sale_order_callups" model="ir.actions.act_window">
            <field name="name">Callups</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">sale.order</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('team_id', '=', 3)]</field>
            <field name="context">{"default_team_id": 3}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new Callup
                </p>
            </field>
            <field name="view_ids" eval="[
                (5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('stairmaster_sale.view_sale_order_callups_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('stairmaster_sale.view_sale_order_callups_form')}),
            ]" />
        </record>
        
        <!-- Update the Callups menu item -->
        <record id="stairmaster_sale.menu_callups_job" model="ir.ui.menu">
            <field name="action" ref="action_sale_order_callups"/>
        </record>
    </data>
</odoo>
