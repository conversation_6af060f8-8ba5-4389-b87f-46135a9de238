<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Warranty Form View -->
        <record id="view_sale_order_warranty_form" model="ir.ui.view">
            <field name="name">sale.order.warranty.form</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="stairmaster_sale.view_order_form_inherit"/>
            <field name="mode">primary</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <!-- Add custom class to form -->
                <form position="attributes">
                    <attribute name="class" add="warranty_form_view" separator=" "/>
                </form>
                
                <!-- Modify Sales Type for Warranty -->
                <field name="sale_type" position="attributes">
                    <attribute name="widget">radio</attribute>
                    <attribute name="options">{'horizontal': true}</attribute>
                    <attribute name="readonly">1</attribute>
                </field>
                
                <!-- Hide certain pages that aren't needed for Warranty -->
                <page name="colour_selections" position="attributes">
                    <attribute name="invisible">1</attribute>
                </page>
                
                <!-- Add a dedicated Warranty information page -->
                <notebook position="inside">
                    <page name="warranty_info" string="Warranty Information">
                        <group>
                            <group>
                                <field name="job_number" required="1"/>
                                <field name="team_id" invisible="0" options="{'no_create': true}"/>
                            </group>
                            <group>
                                <!-- Add warranty-specific fields here in the future -->
                            </group>
                        </group>
                    </page>
                </notebook>
            </field>
        </record>
        
        <!-- Warranty Tree/List View -->
        <record id="view_sale_order_warranty_tree" model="ir.ui.view">
            <field name="name">sale.order.warranty.tree</field>
            <field name="model">sale.order</field>
            <field name="arch" type="xml">
                <list string="Warranty Items">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="job_number"/>
                    <field name="sale_type"/>
                    <field name="stage_id"/>
                    <field name="create_date" optional="show"/>
                </list>
            </field>
        </record>
        
        <!-- Warranty Action -->
        <record id="action_sale_order_warranty" model="ir.actions.act_window">
            <field name="name">Warranty</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">sale.order</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('team_id', '=', 4)]</field>
            <field name="context">{"default_team_id": 4}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new Warranty item
                </p>
            </field>
            <field name="view_ids" eval="[
                (5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('stairmaster_sale.view_sale_order_warranty_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('stairmaster_sale.view_sale_order_warranty_form')}),
            ]" />
        </record>
        
        <!-- Add Warranty menu item -->
        <menuitem
            id="menu_warranty_job"
            name="Warranty"
            action="action_sale_order_warranty"
            parent="stairmaster_sale.job_menu"
            sequence="3"/>
    </data>
</odoo>
