<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Quotes Form View -->
        <record id="view_sale_order_quotes_form" model="ir.ui.view">
            <field name="name">sale.order.quotes.form</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="stairmaster_sale.view_order_form_inherit"/>
            <field name="mode">primary</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <!-- Add custom class to form -->
                <form position="attributes">
                    <attribute name="class" add="quotes_form_view" separator=" "/>
                </form>
                
                <!-- Modify Sales Type for Quotes -->
                <field name="sale_type" position="attributes">
                    <attribute name="widget">selection_badge</attribute>
                </field>
                
                <!-- Control visibility of job type based on team -->
                <field name="quote_request_type_id" position="attributes">
                    <attribute name="required">1</attribute>
                    <attribute name="options">{'no_create': true, 'no_open': true}</attribute>
                </field>
                
                <!-- Show Document Classification page prominently -->
                <page name="document_clasification" position="attributes">
                    <attribute name="sequence">1</attribute>
                </page>
                
                <!-- Make Quote Request Type more prominent -->
                <field name="quote_request_type_id" position="attributes">
                    <attribute name="required">1</attribute>
                    <attribute name="options">{'no_create': true, 'no_open': true}</attribute>
                </field>
                
                <!-- Hide certain fields/pages not relevant to quotes -->
                <page name="other_information" position="attributes">
                    <attribute name="invisible">0</attribute>
                </page>
            </field>
        </record>
        
        <!-- Quotes Tree/List View -->
        <record id="view_sale_order_quotes_tree" model="ir.ui.view">
            <field name="name">sale.order.quotes.tree</field>
            <field name="model">sale.order</field>
            <field name="arch" type="xml">
                <list string="Quotes">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="quote_request_type_id"/>
                    <field name="designer_id"/>
                    <field name="sale_type"/>
                    <field name="stage_id"/>
                    <field name="create_date" optional="show"/>
                </list>
            </field>
        </record>
        
        <!-- Quotes Action -->
        <record id="action_sale_order_quotes" model="ir.actions.act_window">
            <field name="name">Quotes</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">sale.order</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('team_id', '=', 2)]</field>
            <field name="context">{"default_team_id": 2}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new Quote
                </p>
            </field>
            <field name="view_ids" eval="[
                (5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('stairmaster_sale.view_sale_order_quotes_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('stairmaster_sale.view_sale_order_quotes_form')}),
            ]" />
        </record>
        
        <!-- Update the Quotes menu item -->
        <record id="stairmaster_sale.menu_quotes_job" model="ir.ui.menu">
            <field name="action" ref="action_sale_order_quotes"/>
        </record>
    </data>
</odoo>