<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- StairBiz Form View -->
        <record id="view_sale_order_stairbiz_form" model="ir.ui.view">
            <field name="name">sale.order.stairbiz.form</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="stairmaster_sale.view_order_form_inherit"/>
            <field name="mode">primary</field>
            <field name="priority">20</field>
            <field name="arch" type="xml">
                <!-- Add custom class to form -->
                <form position="attributes">
                    <attribute name="class" add="stairbiz_form_view" separator=" "/>
                </form>
                
                <!-- Modify fields display for StairBiz -->
                <group name="partner_details" position="inside">
                    <field name="is_stairbiz" widget="radio" options="{'horizontal': true}" default="yes" readonly="1"/>
                </group>
                
                <!-- Add a dedicated StairBiz information page -->
                <notebook position="inside">
                    <page name="stairbiz_info" string="StairBiz Information">
                        <group>
                            <group>
                                <field name="job_number" required="1"/>
                            </group>
                            <group>
                                <field name="is_measure_sheet" widget="radio" options="{'horizontal': true}"/>
                                <field name="team_id" invisible="0" options="{'no_create': true}"/>
                            </group>
                        </group>
                    </page>
                </notebook>
            </field>
        </record>
        
        <!-- StairBiz Tree/List View -->
        <record id="view_sale_order_stairbiz_tree" model="ir.ui.view">
            <field name="name">sale.order.stairbiz.tree</field>
            <field name="model">sale.order</field>
            <field name="arch" type="xml">
                <list string="StairBiz Orders">
                    <field name="name"/>
                    <field name="partner_id"/>
                    <field name="job_number"/>
                    <field name="designer_id"/>
                    <field name="stage_id"/>
                    <field name="create_date" optional="show"/>
                </list>
            </field>
        </record>
        
        <!-- StairBiz Action -->
        <record id="action_sale_order_stairbiz" model="ir.actions.act_window">
            <field name="name">StairBiz</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">sale.order</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('team_id', '=', 5)]</field>
            <field name="context">{"default_team_id": 5, "default_is_stairbiz": "yes"}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new StairBiz order
                </p>
            </field>
            <field name="view_ids" eval="[
                (5, 0, 0),
                (0, 0, {'view_mode': 'list', 'view_id': ref('stairmaster_sale.view_sale_order_stairbiz_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('stairmaster_sale.view_sale_order_stairbiz_form')}),
            ]" />
        </record>
        
        <!-- Add StairBiz menu item -->
        <menuitem
            id="menu_stairbiz_job"
            name="StairBiz"
            action="action_sale_order_stairbiz"
            parent="stairmaster_sale.job_menu"
            sequence="5"/>
    </data>
</odoo>
