# -*- coding: utf-8 -*-

import base64
import json
import os
import re
import time
import traceback
from datetime import datetime

import magic
import markdownify
import requests
from lxml import html as lxml_html
from odoo import SUPERUSER_ID, _, api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.osv import expression
from odoo.tools.misc import clean_context


class SaleOrder(models.Model):
    _inherit = "sale.order"

    sale_type = fields.Selection(
        [
            ("stairs_balustrade", "Stairs and Balustrade"),
            ("supply_only", "Supply Only"),
        ],
        string="Sales Type",
        tracking=True,
        default="stairs_balustrade",
    )
    # Photo form
    attachment_ids = fields.One2many(
        "ir.attachment",
        "res_id",
        domain=[
            ("res_model", "=", "sale.order"),
            ("sale_attachment_type", "!=", "purchase"),
        ],
        string="Attachments",
        readonly=False,
    )
    attachment_copy_ids = fields.One2many(
        "ir.attachment",
        "res_id",
        domain=[("res_model", "=", "sale.order")],
        string="Attachments Copy",
        readonly=False,
    )
    # Photo - Documents - builder form
    attachment_po_ids = fields.One2many(
        "ir.attachment",
        "res_id",
        domain=[
            ("res_model", "=", "sale.order"),
            ("sale_attachment_type", "=", "purchase"),
        ],
        string="Photo Attachments",
        readonly=False,
    )
    photo = fields.Binary(string="Photo")
    photo_purchase = fields.Binary(string="Purchase Photo")
    is_invisible = fields.Boolean(compute="_compute_is_invisible")
    is_po_invisible = fields.Boolean(compute="_compute_is_po_invisible")
    vat = fields.Char(related="partner_id.vat", string="ABN")

    is_stairbiz = fields.Selection(
        [("yes", "Yes"), ("no", "No")],
        string="Is a Stairbiz file required?",
        default="no",
    )
    is_measure_sheet = fields.Selection(
        [("yes", "Yes"), ("no", "No")],
        string="Is Measure Sheet file required?",
        default="no",
    )
    is_site_specific = fields.Boolean(
        string="Is this a site specific quote?", default=False
    )

    def _domain_item_user_id(self):
        company = self.env.ref("base.main_company", raise_if_not_found=False)
        return [("company_id", "=", company.id)]

    designer_id = fields.Many2one(
        "res.users", string="Designer", tracking=True, domain=_domain_item_user_id
    )

    def action_open_stair_calculator(self):
        self.ensure_one()
        return {
            "type": "ir.actions.act_window",
            "name": "Stair Calculator",
            "res_model": "stair.calculator",
            "view_mode": "form",
            "context": {
                "default_sale_order_id": self.id,
            },
            "target": "new",
        }

    requote_sale_order_id = fields.Many2one(
        "sale.order", string="Requote order", domain="[('partner_id', '=', partner_id)]"
    )
    quote_request_type_id = fields.Many2one(
        "quote.request.type",
        string="Quote Request Type",
        tracking=True,
        domain="[('show_type', '=', True)]",
    )
    show_general_information = fields.Boolean(
        compute="_compute_on_quote_request_type_id"
    )
    show_requote = fields.Boolean(compute="_compute_on_quote_request_type_id")
    show_order_lines = fields.Boolean(compute="_compute_on_quote_request_type_id")
    show_colour_selections = fields.Boolean(compute="_compute_on_quote_request_type_id")

    stage_id = fields.Many2one(
        "sale.order.stage",
        string="Stage",
        index=True,
        tracking=True,
        compute="_compute_stage_id",
        readonly=False,
        store=True,
        copy=False,
        group_expand="_read_group_stage_ids",
        domain="[('team_id', '=', team_id)]",
    )

    @api.onchange("requote_sale_order_id")
    def onchange_requote_sale_order_id(self):
        self = self.with_context(skip_compute_data=True)

    @api.depends("quote_request_type_id")
    def _compute_on_quote_request_type_id(self):
        for rec in self:
            rec.show_general_information = (
                rec.quote_request_type_id
                and rec.quote_request_type_id.show_general_information
                or True
            )
            rec.show_requote = rec.quote_request_type_id.show_requote
            rec.show_order_lines = (
                rec.quote_request_type_id
                and rec.quote_request_type_id.show_order_lines
                or True
            )
            rec.show_colour_selections = (
                rec.quote_request_type_id.show_colour_selections
            )

    # @api.onchange('quote_request_type_id')
    # def onchange_quote_request_type_id(self):
    #     if self.quote_request_type_id:
    #         self.show_order_lines = self.quote_request_type_id.show_order_lines
    #         self.show_general_information = self.quote_request_type_id.show_general_information
    #         self.show_requote = self.quote_request_type_id.show_requote

    @api.depends("team_id")
    def _compute_stage_id(self):
        for sale in self:
            if not sale.stage_id:
                sale.stage_id = sale._stage_find(domain=[("fold", "=", False)]).id

    @api.model
    def _read_group_stage_ids(self, stages, domain):
        team_id = self.env.context.get("default_team_id")
        if not team_id:
            return stages
        search_domain = [("team_id", "=", team_id)]
        stage_ids = stages._search(
            search_domain,
        )
        return stages.browse(stage_ids)

    def _stage_find(self, team_id=False, domain=None, order="sequence, id", limit=1):
        """Determine the stage of the current lead with its teams, the given domain and the given team_id
        :param team_id
        :param domain : base search domain for stage
        :param order : base search order for stage
        :param limit : base search limit for stage
        :returns crm.stage recordset
        """
        # collect all team_ids by adding given one, and the ones related to the current leads
        team_ids = set()
        if team_id:
            team_ids.add(team_id)
        for sale in self:
            if sale.team_id:
                team_ids.add(sale.team_id.id)
        # generate the domain
        if team_ids:
            search_domain = [
                "|",
                ("team_id", "=", False),
                ("team_id", "in", list(team_ids)),
            ]
        else:
            search_domain = [("team_id", "=", False)]
        # AND with the domain in parameter
        if domain:
            search_domain += list(domain)
        # perform search, return the first found
        return self.env["sale.order.stage"].search(
            search_domain, order=order, limit=limit
        )

    measure_sheet_doc_id = fields.Many2one(
        "ir.attachment", string="Measure Sheet Document"
    )
    customer_job_number = fields.Char(string="Customer Job Number", size=20)

    # colour selection fields
    range_option_series = fields.Char(string="Range / Option Series")
    category = fields.Char(string="Category")
    option = fields.Char(string="Option")
    image = fields.Char(string="Image")
    detail_handrail = fields.Char(string="Detail Handrail")
    balustrade = fields.Char(string="Balustrade")
    timber_newel_post = fields.Char(string="Timber Newel Post")
    timber_newel_post_toppers = fields.Char(string="Timber Newel Post Toppers")
    handrail_finish = fields.Char(string="Handrail Finish")
    handrail_style = fields.Char(string="Handrail Style")
    handrail_colour = fields.Char(string="Handrail Colour")
    handrail_application = fields.Char(string="Handrail Application")
    bracket_colour = fields.Char(string="Bracket Colour")

    # Client fields
    client_name = fields.Char(
        string="Client Name",
        size=30,
        store=True,
        readonly=False,
    )
    new_lead = fields.Boolean(string="New Lead", default=True)
    client_note = fields.Text(string="Client Note")
    company_number = fields.Char(string="Company Number", size=20)
    contact_name = fields.Char(string="Contact Name", size=30)
    critical_note = fields.Char(string="Critical Note", size=50)
    cust_id = fields.Char(string="Customer ID", size=15)
    def_contact = fields.Char(string="Default Contact")
    discount = fields.Float(string="Discount")
    client_email = fields.Char(string="Client Email", size=40, readonly=False)
    client_fax = fields.Char(string="Fax", size=21)
    partner_id = fields.Many2one(
        "res.partner",
        string="Customer",
        domain=[("is_company", "=", True), ("type_contact", "=", "builders")],
    )

    combined_document_ids = fields.One2many(
        "groq.document",
        "order_id",
        compute="_compute_combined_documents",
        readonly=False,
        inverse="_inverse_combined_documents",
        string="All Documents",
        store=True,
        index=True,
        precompute=True,
    )

    def _inverse_combined_documents(self):
        for sale in self:
            groq_docs = sale.combined_document_ids
            groq_docs.write(
                {"res_model": "sale.order", "res_id": sale.id, "order_id": sale.id}
            )

    def _compute_combined_documents(self):
        for sale in self:
            groq_documents = self.env["groq.document"].search(
                [("res_model", "=", "sale.order"), ("res_id", "=", sale.id)]
            )
            sale.combined_document_ids = groq_documents

    is_order = fields.Boolean(string="Is Order", compute="_compute_on_team_id")
    is_quote = fields.Boolean(string="Is Quote", compute="_compute_on_team_id")
    # Job fields
    job_date = fields.Date(string="Job Date")
    job_name = fields.Char(string="Job Name", size=30)
    job_number = fields.Char(string="Job Number", size=20)
    job_status = fields.Char(string="Job Status")

    def _compute_on_team_id(self):
        order_team = self.env.ref("stairmaster_sale.sale_order_team_orders")
        quote_team = self.env.ref("stairmaster_sale.sale_order_team_quotes")
        for sale in self:
            sale.is_order = False
            sale.is_quote = False
            if sale.team_id == order_team:
                sale.is_order = True
            elif sale.team_id == quote_team:
                sale.is_quote = True

    @api.depends("attachment_ids")
    def _compute_is_invisible(self):
        for rec in self:
            is_invisible = True
            if rec.attachment_ids:
                is_invisible = False
            rec.is_invisible = is_invisible

    @api.depends("attachment_po_ids")
    def _compute_is_po_invisible(self):
        for rec in self:
            is_po_invisible = True
            if rec.attachment_po_ids:
                is_po_invisible = False
            rec.is_po_invisible = is_po_invisible

    @api.onchange("photo_purchase")
    def onchange_photo_purchase(self):
        if self.photo_purchase:
            self.upload_file(self.photo_purchase)
            self.is_po_invisible = False
            self.photo_purchase = False

    @api.onchange("photo")
    def onchange_photo(self):
        if self.photo:
            self.upload_file(self.photo)
            self.is_invisible = False
            self.photo = False

    @api.onchange("attachment_ids")
    def onchange_attachment_ids(self):
        if not self.attachment_ids:
            self.is_invisible = True

    @api.onchange("attachment_po_ids")
    def onchange_attachment_po_ids(self):
        if not self.attachment_po_ids:
            self.is_po_invisible = True

    def upload_file(self, photo=False):
        if not photo:
            photo = self.photo or self.photo_purchase

        vals = {
            "res_model": self._name,
            "res_id": self._origin,
            "name": "photo",
            "type": "binary",
            "file_display": photo,
        }
        file = (
            self.env["ir.attachment"]
            .sudo()
            .with_context(default_res_model=self._name, default_res_id=self._origin)
            .create(vals)
        )
        file_type = self.detect_file_type(photo)

        if "pdf" in file_type.lower():
            file.instruction_pdf = photo
            file.instruction_type = "pdf"
        elif "image" in file_type.lower():
            file.datas = photo
            file.instruction_type = "image"
        else:
            file.datas = photo
            file.instruction_type = "other"

        if self.photo_purchase:
            file.sale_attachment_type = "purchase"
            attachment_po_ids = self.attachment_po_ids.ids
            attachment_po_ids.append(file.id)
            self.attachment_copy_ids = [(6, 0, attachment_po_ids)]
            self.attachment_po_ids = self.attachment_copy_ids
            self.attachment_copy_ids = False
        else:
            attachment_ids = self.attachment_ids.ids
            attachment_ids.append(file.id)
            self.attachment_copy_ids = [(6, 0, attachment_ids)]
            self.attachment_ids = self.attachment_copy_ids
            self.attachment_copy_ids = False
        return file

    def detect_file_type(self, file_content):
        file_type = magic.from_buffer(base64.b64decode(file_content))
        return file_type

    def write(self, vals):
        """Change the sale_type product according to the selected installation type"""
        res = super().write(vals)
        if "sale_type" in vals:
            self.update_product_base_sale_type()
        return res

    @api.model
    def message_new(self, msg_dict, custom_values=None):
        custom_values = self.update_partner_from_email(msg_dict, custom_values)
        return super(SaleOrder, self).message_new(msg_dict, custom_values)

    @api.model_create_multi
    def create(self, vals_list):
        records = super(SaleOrder, self).create(vals_list)
        records.update_product_base_sale_type()
        return records

    def update_product_base_sale_type(self):
        self.order_line.filtered(
            lambda x: x.product_id and "mfg" in x.product_id.name.lower()
        ).unlink()
        for rec in self:
            # remove the existing sale type
            if not rec.sale_type:
                continue
            # find the lowest sequence of rec
            sequence = rec.order_line.mapped("sequence")
            if sequence and min(sequence) < 0:
                sequence = min(sequence) - 1
            else:
                sequence = -1
            # create a new product
            if rec.sale_type == "stairs_balustrade":
                product = self.env["product.product"].search(
                    [
                        ("name", "=ilike", "mfg"),
                    ],
                    limit=1,
                )
                if product:
                    rec.order_line.create(
                        {
                            "sequence": sequence,
                            "product_id": product.id,
                            "name": product.display_name,
                            "product_uom_qty": 1,
                            "product_uom": product.uom_id.id,
                            "price_unit": product.list_price,
                            "order_id": rec.id,
                        }
                    )

    def action_view_project_ids(self):
        self.ensure_one()
        if not self.order_line:
            return {"type": "ir.actions.act_window_close"}

        sorted_line = self.order_line.sorted("sequence")
        default_sale_line = next(
            sol for sol in sorted_line if sol.product_id.type == "service"
        )
        vals = []
        for line in sorted_line.filtered(lambda line: line.product_id):
            if "mfg" in line.product_template_id.name.lower():
                continue
            vals.append(
                {
                    "product_template_id": line.product_template_id.id,
                    "name": line.name,
                    "product_uom_qty": line.product_uom_qty,
                    "product_uom": line.product_uom.id,
                }
            )
        project_ids = self.with_context(active_test=False).project_ids.filtered(
            lambda p: p.sale_order_id == self and not p.project_line_ids
        )

        project_ids.write({"project_line_ids": [(0, 0, val) for val in vals]})

        action = {
            "type": "ir.actions.act_window",
            "name": ("Projects"),
            "domain": [
                "|",
                ("sale_order_id", "=", self.id),
                ("id", "in", self.with_context(active_test=False).project_ids.ids),
                ("active", "in", [True, False]),
            ],
            "res_model": "project.project",
            "views": [(False, "kanban"), (False, "list"), (False, "form")],
            "view_mode": "kanban,list,form",
            "context": {
                **self._context,
                "default_partner_id": self.partner_id.id,
                "default_sale_line_id": default_sale_line.id,
            },
        }
        if len(self.with_context(active_test=False).project_ids) == 1:
            action.update({"views": [(False, "form")], "res_id": self.project_ids.id})
        return action

    # def extract_documents_colour_selection(self):
    #     groq_document = self.combined_document_ids.filtered(
    #         lambda d: d.document_type_id.name == "colour_selection"
    #         and d.processing_status == "completed"
    #     )
    #     notification = {
    #         "type": "ir.actions.client",
    #         "tag": "display_notification",
    #         "params": {
    #             "title": "Error",
    #             "type": "danger",
    #         },
    #     }
    #     if not groq_document:
    #         notification["params"]["message"] = (
    #             "No Groq document found for the selected document."
    #         )
    #         return notification
    #     # Create mapping groq mapping fields based on processor
    #     groq_document.extract_logs = ""
    #     try:
    #         groq_document.document_type_id.processor_id.groq_processor_process_extracted_text(
    #             groq_document.returned_text
    #         )
    #     except Exception:
    #         notification["params"]["message"] = (
    #             "The document cannot be processed. Please check the groq log for more information."
    #         )
    #         return notification
    #     if groq_document.document_type_id.processor_id.field_mapping_ids:
    #         source_vals_list = (
    #             groq_document.document_type_id.processor_id.field_mapping_ids.read(
    #                 ["source_field", "source_field_value"]
    #             )
    #         )

    #         source_tuple = tuple(
    #             (item["source_field"], item["source_field_value"])
    #             for item in source_vals_list
    #         )
    #         error = []
    #         vals = {}
    #         for source_field, source_field_value in source_tuple:
    #             try:
    #                 if source_field == "Range / Option Series":
    #                     vals.update({"range_option_series": source_field_value})
    #                 if source_field == "Category":
    #                     vals.update({"category": source_field_value})
    #                 if source_field == "Option":
    #                     vals.update({"option": source_field_value})
    #                 if source_field == "Image":
    #                     vals.update({"image": source_field_value})
    #                 if source_field == "Detail Handrail":
    #                     vals.update({"detail_handrail": source_field_value})
    #                 if source_field == "Balustrade":
    #                     vals.update({"balustrade": source_field_value})
    #                 if source_field == "Timber Newel Post":
    #                     vals.update({"timber_newel_post": source_field_value})
    #                 if source_field == "Timber Newel Post Toppers":
    #                     vals.update({"timber_newel_post_toppers": source_field_value})

    #             except Exception as e:
    #                 error.append(e.args[0])
    #                 pass
    #         if vals:
    #             extract_logs = (
    #                 "\n" + "Try create Colour Selection with values: \n" + str(vals)
    #             )
    #             groq_document.update_extracting_log_traceback(extract_logs=extract_logs)
    #             self.env.cr.commit()
    #         self.write(vals)
    #         if error:
    #             notification["params"]["message"] = (
    #                 "Some issue might exist when extract document, please manual check to make sure the extracted value parsed correctly.\n Error: \n"
    #                 + "\n".join(error)
    #             )
    #             notification["params"]["type"] = "info"
    #             return notification

    def action_view_lead(self):
        if self.opportunity_id:
            return {
                "type": "ir.actions.act_window",
                "res_model": "crm.lead",
                "view_mode": "form",
                "view_id": self.env.ref(
                    "stairmaster_crm_stairbiz.view_crm_lead_form_stairbiz"
                ).id,
                "res_id": self.opportunity_id.id,
            }

    def action_extract_documents(self):
        self.ensure_one()
        notification = {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": "Process Extract Documents",
                "type": "info",
            },
        }

        success_docs = []
        failed_docs = []

        for groq_doc in self.combined_document_ids:
            try:
                groq_doc.process_document()
                success_docs.append(groq_doc.name)
            except Exception as e:
                groq_doc.update_extracting_log_traceback(exception=e)
                failed_docs.append(f"{groq_doc.name}: {str(e)}")

        message = ""
        if success_docs:
            message += (
                f"Successfully processed documents:\n- {chr(10).join(success_docs)}\n"
            )

        if failed_docs:
            message += f"\nFailed to process documents:\n- {chr(10).join(failed_docs)}"
            notification["params"]["type"] = "warning"

        notification["params"]["message"] = message or "No documents processed."
        return notification

    def update_sale_order_from_order_quotes_email(self):
        mail_ids = self.message_ids.filtered(lambda a: a.message_type == "email")
        self.stage_id = self.env.ref(
            "stairmaster_sale.sale_order_stage_email_received"
        ).id
        quote_team_id = self.env.ref("stairmaster_sale.sale_order_team_quotes")
        order_team_id = self.env.ref("stairmaster_sale.sale_order_team_orders")
        partner = self.retrive_main_partner(mail_ids)
        if partner:
            self.partner_id = partner
        if self.team_id == quote_team_id:
            self.update_quotes_from_email(mail_ids)
        elif self.team_id == order_team_id:
            self.update_order_from_email(mail_ids)
        self.new_lead = False
        return True

    def update_quotes_from_email(self, mails):
        for mail in mails:
            try:
                self._update_sale_order_from_email(mail)
            except Exception as e:
                traceback_str = "".join(traceback.format_tb(e.__traceback__))
                raise ValidationError(
                    f"Error while processing email: {e} \n {traceback_str}"
                )

    def update_order_from_email(self, mails):
        for mail in mails:
            try:
                self._update_sale_order_from_email(mail)
            except Exception as e:
                traceback_str = "".join(traceback.format_tb(e.__traceback__))
                raise ValidationError(
                    f"Error while processing email: {e} \n {traceback_str}"
                )

    def update_partner_from_email(self, mail, custom_values={}):
        if not custom_values:
            custom_values = {}
        email_from = mail["email_from"]
        if email_from:
            email_match = re.match(r"(?:.*<)?([^@<>]+)@([^@<>]+)(?:>)?", email_from)
            if email_match:
                pre_fix = email_match.group(1)
                domain = email_match.group(2)
                partner = self.env["res.partner"].search(
                    [
                        "|",
                        "|",
                        "&",
                        ("is_company", "=", True),
                        ("email", "=", pre_fix),
                        "&",
                        ("is_company", "=", True),
                        ("email", "ilike", domain),
                        "&",
                        ("is_company", "=", True),
                        ("website", "ilike", domain),
                    ],
                    limit=1,
                )
                # Incase of the email_from can't find it's partner, using this dummy partner to avoid losing the sale.order
                if not partner:
                    partner = self.env.ref("stairmaster_contact_extened.tba_parnter")
                custom_values["partner_id"] = partner and partner.id
        return custom_values

    def _update_sale_order_from_email(self, mail):
        groq_docs = self._create_groq_doc_from_email_content(mail)
        for groq_doc in groq_docs:
            groq_doc.process_document()

    def create_update_groq_type_from_attachment(self, attachment):
        result = attachment.action_classify_document()
        groq_doc = self.env["groq.document"]
        if result.get("groq_document_id"):
            groq_doc = groq_doc.browse(result["groq_document_id"])
        else:
            groq_doc = attachment._create_groq_doc_without_document_type()
            groq_doc.partner_id = self.partner_id.id
        return groq_doc

    def retrive_main_partner(self, mails):
        email = mails[0].email_from
        clarendon_homes = self.env.ref(
            "stairmaster_contact_extened.clarendon_homes_ltd"
        )
        plantation_homes = self.env.ref("stairmaster_contact_extened.plantation_homes")
        coral_homes = self.env.ref("stairmaster_contact_extened.coral_homes")
        for partner in [clarendon_homes, plantation_homes, coral_homes]:
            if email in partner.email:
                return partner
        partner = self.env["res.partner"].search(
            [
                ("is_company", "=", True),
                ("type_contact", "=", "builders"),
                ("email", "like", email),
            ],
            limit=1,
        )
        return partner

    def _create_groq_doc_from_email_content(self, mail):
        groq_docs = self.env["groq.document"]
        # Create groq doc from email title
        if mail.subject:
            groq_docs += self._create_groq_doc_from_email_title(mail)
        # Create groq doc from email body
        if mail.body:
            groq_docs += self._create_groq_doc_from_email_body(mail)
        # Create groq doc from attachments
        attachments = mail.attachment_ids.filtered_domain(
            [("mimetype", "=", "application/pdf")]
        )
        if attachments:
            groq_docs += self.create_groq_doc_from_attachment(attachments)
        return groq_docs

    def _create_groq_doc_from_email_title(self, mail):
        document_type = self.env["groq.document.type"].search(
            [("partner_id", "=", self.partner_id.id), ("name", "ilike", "Email Title")],
            order="name desc",
            limit=1,
        )
        groq_vals = {
            "res_model": self._name,
            "res_id": self.id,
            "partner_id": self.partner_id.id,
            "extracted_content": mail.subject,
            "name": f"Extract - Email Title - {self.id}",
            "groq_document_type_ids": document_type
            and [(6, 0, [document_type.id])]
            or [],
        }
        groq_doc = self.env["groq.document"].create(groq_vals)
        return groq_doc

    def _create_groq_doc_from_email_body(self, mail):
        document_type = self.env["groq.document.type"].search(
            [("partner_id", "=", self.partner_id.id), ("name", "ilike", "Email Body")],
            order="name desc",
            limit=1,
        )
        groq_vals = {
            "res_model": self._name,
            "res_id": self.id,
            "partner_id": self.partner_id.id,
            "extracted_content": markdownify.markdownify(mail.body),
            "name": f"Extract - Email Body - {self.id}",
            "groq_document_type_ids": document_type
            and [(6, 0, [document_type.id])]
            or [],
        }
        groq_doc = self.env["groq.document"].create(groq_vals)
        return groq_doc

    def create_groq_doc_from_attachment(self, attachments):
        groq_doc = self.env["groq.document"]
        for attachment in attachments:
            groq_doc += self.create_update_groq_type_from_attachment(attachment)
        return groq_doc


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    def _timesheet_create_project(self):
        """Generate project for the given so line, and link it.
        :param project: record of project.project in which the task should be created
        :return task: record of the created task
        """
        self.ensure_one()
        values = self._timesheet_create_project_prepare_values()
        if self.product_id.project_template_id:
            values["name"] = "%s - %s" % (
                values["name"],
                self.product_id.project_template_id.name,
            )
            # The no_create_folder context key is used in documents_project
            project = self.product_id.project_template_id.with_context(
                no_create_folder=True
            ).copy(values)
            for line_order in self.order_id.order_line.filtered(
                lambda line: line.product_id
            ):
                if "mfg" in line_order.product_template_id.name.lower():
                    continue
                project.write(
                    {
                        "project_line_ids": [
                            (
                                0,
                                0,
                                {
                                    "product_template_id": line_order.product_template_id.id,
                                    "name": line_order.name,
                                    "product_uom_qty": line_order.product_uom_qty,
                                    "product_uom": line_order.product_uom.id,
                                    "project_id": project.id,
                                },
                            )
                        ]
                    }
                )
            project.tasks.write(
                {
                    "sale_line_id": self.id,
                    "partner_id": self.order_id.partner_id.id,
                }
            )
            # duplicating a project doesn't set the SO on sub-tasks
            project.tasks.filtered("parent_id").write(
                {
                    "sale_line_id": self.id,
                    "sale_order_id": self.order_id.id,
                }
            )
        else:
            project_only_sol_count = self.env["sale.order.line"].search_count(
                [
                    ("order_id", "=", self.order_id.id),
                    (
                        "product_id.service_tracking",
                        "in",
                        ["project_only", "task_in_project"],
                    ),
                ]
            )
            if project_only_sol_count == 1:
                values["name"] = (
                    "%s - [%s] %s"
                    % (
                        values["name"],
                        self.product_id.default_code,
                        self.product_id.name,
                    )
                    if self.product_id.default_code
                    else "%s - %s" % (values["name"], self.product_id.name)
                )
            # The no_create_folder context key is used in documents_project
            project = (
                self.env["project.project"]
                .with_context(no_create_folder=True)
                .create(values)
            )
            for line_order in self.order_id.order_line.filtered(
                lambda line: line.product_id
            ):
                if "mfg" in line_order.product_template_id.name.lower():
                    continue
                project.write(
                    {
                        "project_line_ids": [
                            (
                                0,
                                0,
                                {
                                    "product_template_id": line_order.product_template_id.id,
                                    "name": line_order.name,
                                    "product_uom_qty": line_order.product_uom_qty,
                                    "product_uom": line_order.product_uom.id,
                                    "project_id": project.id,
                                },
                            )
                        ]
                    }
                )

        # Avoid new tasks to go to 'Undefined Stage'
        if not project.type_ids:
            project.type_ids = self.env["project.task.type"].create(
                [
                    {
                        "name": name,
                        "fold": fold,
                        "sequence": sequence,
                    }
                    for name, fold, sequence in [
                        (self.env._("To Do"), False, 5),
                        (self.env._("In Progress"), False, 10),
                        (self.env._("Done"), True, 15),
                        (self.env._("Canceled"), True, 20),
                    ]
                ]
            )

        # link project as generated by current so line
        self.write({"project_id": project.id})
        return project

    def _custom_fetch_related_record(self, document_type, field_model, field_value, field_name):
        record = super()._custom_fetch_related_record(document_type, field_model, field_value, field_name)
        if field_model in ["product.product", "product.template"] and field_name == "product_id":
            record = self._get_product_record(field_value)
        return record

    def _get_product_record(self, field_value):
        Product = self.env["product.product"]
        # Tried to find product by default code
        product = Product.search(
            ["|", ("default_code", "=", field_value), ("name", "ilike", field_value)],
            limit=1,
        )
        if not product:
            product_template = self.env["product.template"].search(
                [
                    "|",
                    ("default_code", "ilike", field_value),
                    ("name", "ilike", field_value),
                ],
                limit=1,
            )
            if product_template:
                product = Product.search(
                    [("product_tmpl_id", "=", product_template.id)], limit=1
                )
        if not product:
            product_attribute_value = self.env["product.attribute.value"].search(
                [("name", "ilike", field_value)], limit=1
            )
            if product_attribute_value:
                product = (
                    self.env["product.template.attribute.value"]
                    .search(
                        [
                            (
                                "product_attribute_value_id",
                                "=",
                                product_attribute_value.id,
                            )
                        ]
                    )
                    .ptav_product_variant_ids[0]
                )
        return product
