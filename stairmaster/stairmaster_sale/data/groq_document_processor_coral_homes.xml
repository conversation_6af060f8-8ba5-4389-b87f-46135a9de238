<odoo>
    <data noupdate="0">

        <!-- Coral Homes Order -->
        <record id="coral_homes_order_processor" model="groq.processor">
            <field name="name">Orders Documents Processor - Coral Homes</field>
            <field name="model_id" ref="groq_document_processor.groq_model_gemma_2_9b"/>
            <field name="partner_id" ref="stairmaster_contact_extened.coral_homes"/>
            <field name="extraction_methods" eval="[(6, 0, [
                ref('groq_document_processor.extraction_method_all_text'),
            ])]"/>
            <field name="system_prompt">
                <![CDATA[
    Extract the text to following fields: product_id, name, product_uom_qty, units, price_unit.
    Example text in text file:
    ```
...
512100C EXTERNAL BALUSTRADE DETAILS 1 Message $0.00* $0.00
512210 STD OPTION 1 EXTERNAL POWDERCOATED 5.41 L/M $454.00 $2,456.14
ALUMINIUM HANDRAIL SINGLE 80x25mm TOP
...

```
The result should be a JSON object like:
{
    "order_line": [
        {
        "product_id": "512100C",
        "name": "EXTERNAL BALUSTRADE DETAILS",
        "product_uom_qty": "0.0",
        "price_unit": "0.0"
    },
    {
        "product_id": "512210",
        "name": "STD OPTION 1 EXTERNAL POWDERCOATED",
        "product_uom_qty": "5.41",
        "price_unit": "454.00"
        }
    ]
}
No extra text should be included in the result.
                ]]>
            </field>
        </record>
        <record id="coral_homes_order_type" model="groq.document.type">
            <field name="name">Orders Documents - Coral Homes</field>
            <field name="model_name">sale.order</field>
            <field name="partner_id" ref="stairmaster_contact_extened.coral_homes"/>
            <field name="processor_id" ref="coral_homes_order_processor"/>
            <field name="extract_type">text</field>
        </record>

        <!-- Coral Homes Get Link -->
        <record id="coral_homes_get_link_processor" model="groq.processor">
            <field name="name">Get Link Processor - Coral Homes</field>
            <field name="model_id" ref="groq_document_processor.groq_model_gemma_2_9b"/>
            <field name="partner_id" ref="stairmaster_contact_extened.coral_homes"/>
            <field name="extraction_methods" eval="[(6, 0, [
                ref('groq_document_processor.extraction_method_all_text'),
            ])]"/>
            <field name="system_prompt">
                <![CDATA[
Parse the whole set and get the https links and Customer name of the parts below, if any is empty or NA leave it empty: CONSTRUCTION PLANS, COLOUR SELECTION, BALCONY DETAIL, BRACING DESIGN, CONSTRUCTION DETAILS
Example text in text file:
```
...
IMPORTANT NOTE: Balustrade installer to ensure
that base plates are installed at lug installation stage
to enable wet seal to be applied over the base plates
at balcony wet seal stage.
CONSTRUCTION PLANS
https://tinyurl.com/226brs3r
COLOUR SELECTION
https://tinyurl.com/2azsljmz
BALCONY DETAIL
https://tinyurl.com/24s7w8z7
BRACING DESIGN
NA
CONSTRUCTION DETAILS
https://tinyurl.com/255hgmbs
512100C EXTERNAL BALUSTRADE DETAILS 1 Message $0.00* $0.00
512210 STD OPTION 1 EXTERNAL POWDERCOATED 5.41 L/M $454.00 $2,456.14
ALUMINIUM HANDRAIL SINGLE 80x25mm TOP
This order is subject to Standard Purchase Order Terms
Please direct order queries to: <EMAIL>
Page1 of 4

...

```
The result should be a JSON object like:
    {
        "CONSTRUCTION PLANS": "https://tinyurl.com/226brs3r",
        "COLOUR SELECTION": "https://tinyurl.com/2azsljmz",
        "BALCONY DETAIL": "https://tinyurl.com/24s7w8z7",
        "BRACING DESIGN": "null",
        "CONSTRUCTION DETAILS": "https://tinyurl.com/255hgmbs"
    }
No extra text should be included in the result.
                ]]>
            </field>
        </record>
        <record id="coral_homes_get_link_order_type" model="groq.document.type">
            <field name="name">Get Link Documents - Coral Homes</field>
            <field name="model_name">sale.order</field>
            <field name="partner_id" ref="stairmaster_contact_extened.coral_homes"/>
            <field name="processor_id" ref="coral_homes_get_link_processor"/>
            <field name="extract_type">url</field>
        </record>

        <!-- Coral Homes Colour Selection -->
        <record id="coral_homes_colour_selection_processor" model="groq.processor">
            <field name="name">Colour Selection Processor - Coral Homes</field>
            <field name="model_id" ref="groq_document_processor.groq_model_gemma_2_9b"/>
            <field name="partner_id" ref="stairmaster_contact_extened.coral_homes"/>
            <field name="extraction_methods" eval="[(6, 0, [
                ref('groq_document_processor.extraction_method_specific_pages'),
            ])]"/>
            <field name="specific_pages">2-5</field>
            <field name="system_prompt">
                <![CDATA[
Look into table and into the STAIRCASE section, return category title and category's value right next to it's category, below is only categories title it ever have:
**Range / Option Series:
**Category:
**Option:
**Image:
**Detail Handrail:
**Balustrade:
**Timber Newel Post:
**Timber Newel Post Toppers:
Only fill in what is clearly mentioned in different dictionaries for each category, no extra text or explanation
The result should be a JSON object like:
```json
{
    "range_option_series": <value>,
    "category": <value>,
    "option": <value>,
    "image": <value>,
    "detail_handrail": <value>,
    "balustrade": <value>,
    "timber_newel_post": <value>,
    "timber_newel_post_toppers": <value>
}
```
No extra text should be included in the result.
                ]]>
            </field>
        </record>
        <record id="coral_homes_colour_selection_type" model="groq.document.type">
            <field name="name">Colour Selection Documents - Coral Homes</field>
            <field name="model_name">sale.order</field>
            <field name="partner_id" ref="stairmaster_contact_extened.coral_homes"/>
            <field name="processor_id" ref="coral_homes_colour_selection_processor"/>
            <field name="extract_type">text</field>
        </record>


        <record id="coral_homes_email_body_standard_processor" model="groq.processor">
            <field name="name">Email Body Standard Processor - Coral Homes</field>
            <field name="model_id" ref="groq_document_processor.groq_model_gemma_2_9b"/>
            <field name="partner_id" ref="stairmaster_contact_extened.coral_homes"/>
            <field name="extraction_methods" eval="[(6, 0, [
                ref('groq_document_processor.extraction_method_all_text'),
            ])]"/>
            <field name="system_prompt">
                <![CDATA[
Extract the following fields from the input text and return them as a JSON object:
site_address: the full site/project address.
customer_job_number: the project code.
date_order: the date listed under "For date".
Example text:
Project code:	Q40591
Project name:	Lot 103, 80 Kent Street HAMILTON QLD 4007
PO number:	Q40591/435.51
Supplier:	STAIRMASTER (QLD) PTY LTD - ABN: ***********
Activity name:	PRIVACY SCREENS
Amount (exc. GST):	$1,330.53
For date:	29 Apr 2025
Comments:	Provision to supply and install of balustrade screens due to the original screen was not barrier rated . Quote 24087 17/04/2025.
Supervisor:	Sean Hallinan

The result should be a JSON object like:
```
{
"site_addess": "Lot 103, 80 Kent Street HAMILTON QLD 4007",
"customer_job_number": "Q40591",
"date_order": "	29 Apr 2025",
}
```
No extra text should be included in the result.
                ]]>
            </field>
        </record>
        <record id="coral_homes_email_body_standard_type" model="groq.document.type">
            <field name="name">Email Body Standard - Coral Homes</field>
            <field name="model_name">sale.order</field>
            <field name="partner_id" ref="stairmaster_contact_extened.coral_homes"/>
            <field name="processor_id" ref="coral_homes_email_body_standard_processor"/>
            <field name="extract_type">text</field>
        </record>

        
        <record id="coral_homes_email_body_standard_classification_rule" model="document.classification.rule">
            <field name="active">1</field>
            <field name="classification_count">3</field>
            <field name="confidence_threshold">0.9</field>
            <field name="filename_pattern">0</field>
            <field name="groq_document_type_id" ref="coral_homes_email_body_standard_type"/>
            <field name="is_global">0</field>
            <field name="custom_prompt">
                <![CDATA[
You are an AI assistant specialized in document classification.
Analyze this document based on document content

Document content: {content}

Please classify this document by determining if document content like email converstation.
Provide your classification, confidence level (0-1), and explanation.
Format your response as JSON:
{{
    "document_type": "exact name of the matching document type",
    "confidence": 0.99,
    "analysis": "Brief explanation of why this classification was chosen"
}}
                ]]>
            </field>
            <field name="name">Email Body Standard - Coral Homes Rule</field>
            <field name="partner_id" ref="stairmaster_contact_extened.coral_homes"/>
        </record>
    </data>
</odoo>
