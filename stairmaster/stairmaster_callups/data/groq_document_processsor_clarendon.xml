<odoo>
    <data noupdate="0">
         <record id="callups_job_number_title_processor_clarendon_homes_ltd" model="groq.processor">
            <field name="name">Email Callups Title Processor - Clarendon Homes</field>
            <field name="model_id" ref="groq_document_processor.groq_model_gemma_2_9b"/>
            <field name="extraction_methods" eval="[(6, 0, [
                ref('groq_document_processor.extraction_method_all_text'),
            ])]"/>
            <field name="system_prompt">
                <![CDATA[
Extract the address from the email title, no extra text should be included in the result.
Example email subject: 49905951-Lot 1544, [#37] Hamilton Drive JACOBS WELL QLD 4208
Please refer to job number 33390 for the details and the result should be a JSON object like:
{
    "customer_job_number": "49905951",
    "site_street": "Lot 1544, [#37] Hamilton Drive JACOBS WELL QLD 4208",
}
Example email subject: TRADE ACTION ITEMS 12 MTH MTCE | JOB# 4692 - 22 Cradle Place SPRING MOUNTAIN
Please refer to job number 33390 for the details and the result should be a JSON object like:
{
    "customer_job_number": "4692",
    "site_street": "22 Cradle Place SPRING MOUNTAIN",
}

No extra text should be included in the result.
                ]]>
            </field>
        </record>
        <record id="callups_email_title_type_clarendon_homes_ltd" model="groq.document.type">
            <field name="name">Email Callups Title - Clarendon Homes</field>
            <field name="model_name">sale.order</field>
            <field name="partner_id" ref="stairmaster_contact_extened.clarendon_homes_ltd"/>
            <field name="extract_type">text</field>
            <field name="processor_id" ref="callups_job_number_title_processor_clarendon_homes_ltd"/>
        </record>


        <record id="callups_email_body_processor_clarendon_homes_ltd" model="groq.processor">
            <field name="name">Email Callups Body Processor - Clarendon Homes</field>
            <field name="model_id" ref="groq_document_processor.groq_model_gemma_2_9b"/>
            <field name="extraction_methods" eval="[(6, 0, [
            ref('groq_document_processor.extraction_method_all_text'),
            ])]"/>
            <field name="system_prompt">
                <![CDATA[
Extract the datetime and address from the email title and body.
Please refer to job number and client street for the details
No extra text should be included in the result.
Example email body:
```
Good afternoon
Ron (maintenance manager) has attended the above address for a maintenance review. Below are the items requiring your attention please.
Ron can be contacted on 0411 575 557 if you have any questions.

* If there is a possibility of a service call / call out charge, please gain prior approval from the home owner as Ownit Homes will not accept any charges to site *.

Home owner: JIMMY WONG - 0432 885 622

Please attend on the date nominated as the clients have arranged time off work on this date to see completion.

****Plasterer
        Monday 2/12/2024 at 08:00am

All other trades
        Tuesday 10/12/2024 at 08:00am

Number below relates to attached PDF #
```
Convert the time value into datetime format, example:
"Wednesday 18/12/2024 at 08:00am" converted to "2024-12-18 08:00 am"
"Tuesday 10/12/2024 at 08:00am" converted to "2024-12-10 08:00 am"

The result extract from email body example should be a JSON object like:
```
{
    "callups_request_date": "2024-12-02 08:00 am",
    "site_street": "22 Cradle Place SPRING MOUNTAIN",
}
```
                ]]>
            </field>
        </record>
        <record id="callups_email_body_type_clarendon_homes_ltd" model="groq.document.type">
            <field name="name">Email Callups Body - Clarendon Homes</field>
            <field name="model_name">sale.order</field>
            <field name="partner_id" ref="stairmaster_contact_extened.clarendon_homes_ltd"/>
            <field name="extract_type">text</field>
            <field name="processor_id" ref="callups_email_body_processor_clarendon_homes_ltd"/>
        </record>

        <record id="callups_report_processor_clarendon_homes_ltd" model="groq.processor">
            <field name="name">Callups Report Processor - Clarendon Homes</field>
            <field name="model_id" ref="groq_document_processor.groq_model_gemma_2_9b"/>
            <field name="extraction_methods" eval="[(6, 0, [
                ref('groq_document_processor.extraction_method_all_text'),
            ])]"/>
            <field name="system_prompt">
                <![CDATA[
Extract the following structured information from the document content:
partner_id (the company requesting the job, e.g. "Clarendon Homes")
job_number (e.g. "49906045")
email (contact email, e.g. "<EMAIL>")
site_street (job location address, e.g. "Lot 3 [#109] Waterview Avenue WYNNUM QLD 4178")
callups_request_date (the new scheduled task date, e.g. "12/05/2025")
Return the result in the following JSON format:
```
{
  "partner_id": "",
  "job_number": "",
  "email": "",
  "site_street": "",
  "callups_request_date": ""
}
```
Expected output based on the file:
```
{
  "partner_id": "Clarendon Homes",
  "job_number": "49906045",
  "email": "<EMAIL>",
  "site_street": "Lot 3 [#109] Waterview Avenue WYNNUM QLD 4178",
  "callups_request_date": "12/05/2025"
}
```
No extra text should be included in the result.
                ]]>
            </field>
        </record>

        <record id="callups_report_clarendon_homes_ltd" model="groq.document.type">
            <field name="name">Callups Report - Clarendon Homes</field>
            <field name="model_name">sale.order</field>
            <field name="partner_id" ref="stairmaster_contact_extened.clarendon_homes_ltd"/>
            <field name="extract_type">text</field>
            <field name="processor_id" ref="callups_report_processor_clarendon_homes_ltd"/>
        </record>
        
        <record id="callups_report_classification_rule_clarendon_homes_ltd" model="document.classification.rule">
            <field name="active">1</field>
            <field name="classification_count">3</field>
            <field name="confidence_threshold">0.97</field>
            <field name="filename_pattern">0</field>
            <field name="groq_document_type_id" ref="callups_report_clarendon_homes_ltd"/>
            <field name="is_global">0</field>
            <field name="name">Callup Reports Clarendon Homes</field>
            <field name="filename_pattern">(?i)^CallUpReport\.pdf$</field>
            <field name="partner_id" ref="stairmaster_contact_extened.clarendon_homes_ltd"/>
            <field name="custom_prompt">
                <![CDATA[
You are an AI assistant specialized in document classification.
Analyze this document based on the title

Document filename: {filename}

Please classify this document by determining the most appropriate document type from the list above.
Provide your classification, confidence level (0-1), and explanation.
Format your response as JSON:
{{
    "document_type": "exact name of the matching document type",
    "confidence": 0.99,
    "analysis": "Brief explanation of why this classification was chosen"
}}
                ]]>
            </field>
        </record>
    </data>
</odoo>
