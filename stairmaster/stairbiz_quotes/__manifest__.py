{
    "name": "StairBiz quotes",
    "version": "********.0",
    "summary": "Integration with StairBiz Server",
    "description": "This module handles quotes data for StairBiz.",
    "author": "Your Name",
    "category": "Custom",
    "depends": ["base", "mail", "stairmaster_contact_extened"],
    "data": [
        "security/ir.model.access.csv",
        "data/base_automation.xml",
        "views/stairbiz_quotes_client_views.xml",
        "views/stairbiz_quotes_sysdiagram_views.xml",
        "views/stairbiz_quotes_actgroup_views.xml",
        "views/stairbiz_quotes_activity_views.xml",
        "views/stairbiz_quotes_clientcontact_views.xml",
        "views/stairbiz_quotes_detail_views.xml",
        "views/stairbiz_quotes_done_views.xml",
        "views/stairbiz_quotes_inventory_views.xml",
        "views/stairbiz_quotes_jobfolder_views.xml",
        "views/stairbiz_quotes_joblabor_views.xml",
        "views/stairbiz_quotes_job_views.xml",
        "views/stairbiz_quotes_mydatafield_views.xml",
        "views/stairbiz_quotes_mydatafieldvalue_views.xml",
        "views/stairbiz_quotes_note_views.xml",
        "views/stairbiz_quotes_payment_views.xml",
        "views/stairbiz_quotes_project_views.xml",
        "views/stairbiz_quotes_quotation_views.xml",
        "views/stairbiz_quotes_sharedconfig_views.xml",
        "views/stairbiz_quotes_sharedconfigctl_views.xml",
        "views/stairbiz_quotes_site_views.xml",
        "views/stairbiz_quotes_snua_views.xml",
        "views/stairbiz_quotes_user_views.xml",
        "views/stairbiz_quotes_version_views.xml",
        "views/stairbiz_quotes_blobsshared_exporterrors_views.xml",
        "views/stairbiz_quotes_menu_views.xml",
        "views/stairbiz_integration_config_views.xml",
        "views/stairbiz_stairbiz_views.xml",
        "views/stairbiz_preview_wizard_views.xml",
        "views/res_config_settings_views.xml",

    ],
    "installable": True,
    "license": "LGPL-3",
    "application": True,
}
